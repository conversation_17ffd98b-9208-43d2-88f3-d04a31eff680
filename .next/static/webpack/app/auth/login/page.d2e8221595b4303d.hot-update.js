"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./components/language-provider.tsx":
/*!******************************************!*\
  !*** ./components/language-provider.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst translations = {\n    sq: {\n        // Navigation\n        \"nav.dashboard\": \"Paneli Kryesor\",\n        \"nav.calendar\": \"Kalendari\",\n        \"nav.book\": \"Rezervo\",\n        \"nav.activeBookings\": \"Rezervimet Aktive\",\n        \"nav.cars\": \"Makinat\",\n        \"nav.damageReports\": \"Raportet e Dëmeve\",\n        \"nav.wallet\": \"Portofoli\",\n        \"nav.settings\": \"Cilësimet\",\n        \"nav.users\": \"Përdoruesit\",\n        \"nav.clients\": \"Klientët\",\n        // Auth\n        \"auth.login\": \"Hyrje\",\n        \"auth.register\": \"Regjistrohu\",\n        \"auth.email\": \"Email\",\n        \"auth.password\": \"Fjalëkalimi\",\n        \"auth.confirmPassword\": \"Konfirmo Fjalëkalimin\",\n        \"auth.fullName\": \"Emri i Plotë\",\n        \"auth.signIn\": \"Hyr\",\n        \"auth.signUp\": \"Regjistrohu\",\n        \"auth.noAccount\": \"Nuk keni llogari?\",\n        \"auth.hasAccount\": \"Keni tashmë llogari?\",\n        // Dashboard\n        \"dashboard.welcome\": \"Mirë se vini në OR_EL\",\n        \"dashboard.totalCars\": \"Makinat Gjithsej\",\n        \"dashboard.activeBookings\": \"Rezervimet Aktive\",\n        \"dashboard.totalRevenue\": \"Të Ardhurat Gjithsej\",\n        \"dashboard.pendingReports\": \"Raportet në Pritje\",\n        // Common\n        \"common.search\": \"Kërko...\",\n        \"common.add\": \"Shto\",\n        \"common.edit\": \"Ndrysho\",\n        \"common.delete\": \"Fshi\",\n        \"common.save\": \"Ruaj\",\n        \"common.cancel\": \"Anulo\",\n        \"common.loading\": \"Duke ngarkuar...\",\n        \"common.view\": \"Shiko\",\n        \"common.filter\": \"Filtro\",\n        \"common.all\": \"Të gjitha\",\n        \"common.available\": \"I disponueshëm\",\n        \"common.rented\": \"I dhënë me qira\",\n        \"common.maintenance\": \"Mirëmbajtje\",\n        \"common.active\": \"Aktiv\",\n        \"common.confirmed\": \"I konfirmuar\",\n        \"common.pending\": \"Në pritje\",\n        \"common.completed\": \"I përfunduar\",\n        // Booking\n        \"booking.title\": \"Krijo Rezervim të Ri\",\n        \"booking.clientName\": \"Emri i Klientit\",\n        \"booking.clientPhone\": \"Telefoni i Klientit\",\n        \"booking.selectCar\": \"Zgjidh Makinën\",\n        \"booking.startDate\": \"Data e Fillimit\",\n        \"booking.endDate\": \"Data e Përfundimit\",\n        \"booking.time\": \"Koha\",\n        \"booking.location\": \"Vendndodhja e Marrjes\",\n        \"booking.notes\": \"Shënime Shtesë\",\n        \"booking.summary\": \"Përmbledhja e Rezervimit\",\n        \"booking.duration\": \"Kohëzgjatja\",\n        \"booking.dailyRate\": \"Tarifa Ditore\",\n        \"booking.totalAmount\": \"Shuma Totale\",\n        \"booking.create\": \"Krijo Rezervim\",\n        \"booking.searchCars\": \"Kërko makina sipas markës, modelit...\",\n        \"booking.filterByType\": \"Filtro sipas llojit\",\n        \"booking.filterByBrand\": \"Filtro sipas markës\",\n        \"booking.availableCars\": \"Makinat e Disponueshme\",\n        \"booking.noAvailableCars\": \"Nuk ka makina të disponueshme\",\n        // Car Types\n        \"carType.luxury\": \"Luksoze\",\n        \"carType.suv\": \"SUV\",\n        \"carType.sedan\": \"Sedan\",\n        \"carType.compact\": \"Kompakte\",\n        \"carType.electric\": \"Elektrike\",\n        \"carType.sports\": \"Sportive\",\n        // Calendar\n        \"calendar.title\": \"Kalendari i Rezervimeve\",\n        \"calendar.selectDates\": \"Kliko për të zgjedhur periudhën e datave\",\n        \"calendar.selectedPeriod\": \"Periudha e zgjedhur\",\n        \"calendar.clickEndDate\": \"Kliko datën e përfundimit\",\n        \"calendar.createBooking\": \"Krijo Rezervim\",\n        \"calendar.clearSelection\": \"Pastro Zgjedhjen\",\n        \"calendar.todaysBookings\": \"Rezervimet e Sotme\",\n        \"calendar.noBookingsToday\": \"Nuk ka rezervime sot\",\n        \"calendar.quickStats\": \"Statistika të Shpejta\",\n        \"calendar.thisMonth\": \"Këtë Muaj\",\n        \"calendar.revenue\": \"Të Ardhurat\",\n        \"calendar.availableCars\": \"Makinat e Disponueshme\",\n        // Active Bookings\n        \"activeBookings.title\": \"Rezervimet Aktive\",\n        \"activeBookings.subtitle\": \"Pamje kompakte e të gjitha rezervimeve\",\n        \"activeBookings.searchBookings\": \"Kërko rezervime...\",\n        \"activeBookings.booking\": \"REZERVIMI\",\n        \"activeBookings.client\": \"KLIENTI\",\n        \"activeBookings.car\": \"MAKINA\",\n        \"activeBookings.dates\": \"DATAT\",\n        \"activeBookings.location\": \"VENDNDODHJA\",\n        \"activeBookings.status\": \"STATUSI\",\n        \"activeBookings.amount\": \"SHUMA\",\n        \"activeBookings.actions\": \"VEPRIMET\",\n        \"activeBookings.complete\": \"Përfundo\",\n        // Cars\n        \"cars.title\": \"Makinat\",\n        \"cars.subtitle\": \"Menaxhimi kompakt i flotës\",\n        \"cars.allBrands\": \"Të gjitha Markat\",\n        \"cars.allModels\": \"Të gjitha Modelet\",\n        \"cars.searchCars\": \"Kërko makina...\",\n        \"cars.addCar\": \"Shto Makinë\",\n        \"cars.details\": \"DETAJET\",\n        \"cars.rate\": \"TARIFA\",\n        // Damage Reports\n        \"damageReports.title\": \"Raportet e Dëmeve\",\n        \"damageReports.subtitle\": \"Sistemi kompakt i ndjekjes së dëmeve\",\n        \"damageReports.searchReports\": \"Kërko raporte...\",\n        \"damageReports.newReport\": \"Raport i Ri\",\n        \"damageReports.report\": \"RAPORTI\",\n        \"damageReports.damage\": \"DËMI\",\n        \"damageReports.severity\": \"RËNDËSIA\",\n        \"damageReports.cost\": \"KOSTOJA\",\n        \"damageReports.inRepair\": \"Në Riparim\",\n        \"damageReports.minor\": \"I vogël\",\n        \"damageReports.moderate\": \"Mesatar\",\n        \"damageReports.major\": \"I madh\",\n        // Clients\n        \"clients.title\": \"Klientët\",\n        \"clients.subtitle\": \"Menaxho bazën e të dhënave të klientëve\",\n        \"clients.searchClients\": \"Kërko klientë sipas emrit, email, ose telefonit...\",\n        \"clients.addClient\": \"Shto Klient\",\n        \"clients.totalClients\": \"Klientët Gjithsej\",\n        \"clients.activeClients\": \"Klientët Aktivë\",\n        \"clients.vipClients\": \"Klientët VIP\",\n        \"clients.avgRating\": \"Vlerësimi Mesatar\",\n        \"clients.totalBookings\": \"Rezervimet Gjithsej\",\n        \"clients.totalSpent\": \"Shuma e Shpenzuar\",\n        \"clients.lastBooking\": \"Rezervimi i Fundit\",\n        \"clients.preferredCars\": \"Makinat e Preferuara\",\n        \"clients.viewProfile\": \"Shiko Profilin\",\n        \"clients.editClient\": \"Ndrysho Klientin\",\n        \"clients.newBooking\": \"Rezervim i Ri\",\n        \"clients.joined\": \"U bashkua\",\n        \"clients.inactive\": \"Joaktiv\",\n        \"clients.vip\": \"VIP\",\n        // Wallet\n        \"wallet.title\": \"Portofoli\",\n        \"wallet.subtitle\": \"Ndiq transaksionet financiare dhe të ardhurat\",\n        \"wallet.totalBalance\": \"Bilanci Total\",\n        \"wallet.thisMonthIncome\": \"Të Ardhurat e Këtij Muaji\",\n        \"wallet.pendingPayments\": \"Pagesat në Pritje\",\n        \"wallet.totalExpenses\": \"Shpenzimet Totale\",\n        \"wallet.recentTransactions\": \"Transaksionet e Fundit\",\n        \"wallet.latestActivities\": \"Aktivitetet e fundit financiare\",\n        \"wallet.quickActions\": \"Veprime të Shpejta\",\n        \"wallet.addIncome\": \"Shto të Ardhura\",\n        \"wallet.recordExpense\": \"Regjistro Shpenzim\",\n        \"wallet.schedulePayment\": \"Planifiko Pagesë\",\n        \"wallet.monthlySummary\": \"Përmbledhja Mujore\",\n        \"wallet.totalIncome\": \"Të Ardhurat Totale\",\n        \"wallet.netProfit\": \"Fitimi Neto\",\n        \"wallet.export\": \"Eksporto\",\n        // Profile & Settings\n        \"profile.profile\": \"Profili\",\n        \"profile.settings\": \"Cilësimet\",\n        \"profile.users\": \"Përdoruesit\",\n        \"profile.logout\": \"Dil\",\n        \"profile.notifications\": \"Njoftimet\",\n        \"profile.administrator\": \"Administrator\",\n        // Notifications\n        \"notifications.title\": \"Njoftimet\",\n        \"notifications.newBookingRequest\": \"Kërkesë e re për rezervim\",\n        \"notifications.bookingEnding\": \"Rezervimi po përfundon\",\n        \"notifications.bookingEnded\": \"Rezervimi përfundoi\",\n        \"notifications.carMaintenanceDue\": \"Mirëmbajtja e makinës është e nevojshme\",\n        \"notifications.paymentReceived\": \"Pagesa u mor\",\n        \"notifications.damageReported\": \"U raportua dëm\",\n        \"notifications.markAllRead\": \"Shëno të gjitha si të lexuara\",\n        \"notifications.noNotifications\": \"Nuk ka njofrime\",\n        // Time\n        \"time.now\": \"tani\",\n        \"time.minutesAgo\": \"minuta më parë\",\n        \"time.hoursAgo\": \"orë më parë\",\n        \"time.daysAgo\": \"ditë më parë\",\n        \"time.days\": \"ditë\",\n        \"time.hours\": \"orë\",\n        // Status\n        \"status.available\": \"I disponueshëm\",\n        \"status.rented\": \"I dhënë me qira\",\n        \"status.maintenance\": \"Mirëmbajtje\",\n        \"status.active\": \"Aktiv\",\n        \"status.confirmed\": \"I konfirmuar\",\n        \"status.pending\": \"Në pritje\",\n        \"status.completed\": \"I përfunduar\",\n        \"status.cancelled\": \"I anuluar\",\n        \"status.paid\": \"I paguar\",\n        \"status.overdue\": \"I vonuar\",\n        // Months\n        \"month.january\": \"Janar\",\n        \"month.february\": \"Shkurt\",\n        \"month.march\": \"Mars\",\n        \"month.april\": \"Prill\",\n        \"month.may\": \"Maj\",\n        \"month.june\": \"Qershor\",\n        \"month.july\": \"Korrik\",\n        \"month.august\": \"Gusht\",\n        \"month.september\": \"Shtator\",\n        \"month.october\": \"Tetor\",\n        \"month.november\": \"Nëntor\",\n        \"month.december\": \"Dhjetor\",\n        // Days\n        \"day.sunday\": \"E Diel\",\n        \"day.monday\": \"E Hënë\",\n        \"day.tuesday\": \"E Martë\",\n        \"day.wednesday\": \"E Mërkurë\",\n        \"day.thursday\": \"E Enjte\",\n        \"day.friday\": \"E Premte\",\n        \"day.saturday\": \"E Shtunë\",\n        \"day.sun\": \"Die\",\n        \"day.mon\": \"Hën\",\n        \"day.tue\": \"Mar\",\n        \"day.wed\": \"Mër\",\n        \"day.thu\": \"Enj\",\n        \"day.fri\": \"Pre\",\n        \"day.sat\": \"Sht\"\n    },\n    en: {\n        // Navigation\n        \"nav.calendar\": \"Calendar\",\n        \"nav.book\": \"Book\",\n        \"nav.activeBookings\": \"Active Bookings\",\n        \"nav.cars\": \"Cars\",\n        \"nav.damageReports\": \"Damage Reports\",\n        \"nav.wallet\": \"Wallet\",\n        \"nav.settings\": \"Settings\",\n        \"nav.users\": \"Users\",\n        \"nav.clients\": \"Clients\",\n        // Auth\n        \"auth.login\": \"Login\",\n        \"auth.register\": \"Register\",\n        \"auth.email\": \"Email\",\n        \"auth.password\": \"Password\",\n        \"auth.confirmPassword\": \"Confirm Password\",\n        \"auth.fullName\": \"Full Name\",\n        \"auth.signIn\": \"Sign In\",\n        \"auth.signUp\": \"Sign Up\",\n        \"auth.noAccount\": \"Don't have an account?\",\n        \"auth.hasAccount\": \"Already have an account?\",\n        // Dashboard\n        \"dashboard.welcome\": \"Welcome to OR_EL\",\n        \"dashboard.totalCars\": \"Total Cars\",\n        \"dashboard.activeBookings\": \"Active Bookings\",\n        \"dashboard.totalRevenue\": \"Total Revenue\",\n        \"dashboard.pendingReports\": \"Pending Reports\",\n        // Common\n        \"common.search\": \"Search...\",\n        \"common.add\": \"Add\",\n        \"common.edit\": \"Edit\",\n        \"common.delete\": \"Delete\",\n        \"common.save\": \"Save\",\n        \"common.cancel\": \"Cancel\",\n        \"common.loading\": \"Loading...\",\n        \"common.view\": \"View\",\n        \"common.filter\": \"Filter\",\n        \"common.all\": \"All\",\n        \"common.available\": \"Available\",\n        \"common.rented\": \"Rented\",\n        \"common.maintenance\": \"Maintenance\",\n        \"common.active\": \"Active\",\n        \"common.confirmed\": \"Confirmed\",\n        \"common.pending\": \"Pending\",\n        \"common.completed\": \"Completed\",\n        // Booking\n        \"booking.title\": \"Create New Booking\",\n        \"booking.clientName\": \"Client Name\",\n        \"booking.clientPhone\": \"Client Phone\",\n        \"booking.selectCar\": \"Select Car\",\n        \"booking.startDate\": \"Start Date\",\n        \"booking.endDate\": \"End Date\",\n        \"booking.time\": \"Time\",\n        \"booking.location\": \"Pickup Location\",\n        \"booking.notes\": \"Additional Notes\",\n        \"booking.summary\": \"Booking Summary\",\n        \"booking.duration\": \"Duration\",\n        \"booking.dailyRate\": \"Daily Rate\",\n        \"booking.totalAmount\": \"Total Amount\",\n        \"booking.create\": \"Create Booking\",\n        \"booking.searchCars\": \"Search cars by make, model...\",\n        \"booking.filterByType\": \"Filter by type\",\n        \"booking.filterByBrand\": \"Filter by brand\",\n        \"booking.availableCars\": \"Available Cars\",\n        \"booking.noAvailableCars\": \"No available cars found\",\n        // Car Types\n        \"carType.luxury\": \"Luxury\",\n        \"carType.suv\": \"SUV\",\n        \"carType.sedan\": \"Sedan\",\n        \"carType.compact\": \"Compact\",\n        \"carType.electric\": \"Electric\",\n        \"carType.sports\": \"Sports\",\n        // Calendar\n        \"calendar.title\": \"Booking Calendar\",\n        \"calendar.selectDates\": \"Click to select date range\",\n        \"calendar.selectedPeriod\": \"Selected Period\",\n        \"calendar.clickEndDate\": \"Click end date\",\n        \"calendar.createBooking\": \"Create Booking\",\n        \"calendar.clearSelection\": \"Clear Selection\",\n        \"calendar.todaysBookings\": \"Today's Bookings\",\n        \"calendar.noBookingsToday\": \"No bookings today\",\n        \"calendar.quickStats\": \"Quick Stats\",\n        \"calendar.thisMonth\": \"This Month\",\n        \"calendar.revenue\": \"Revenue\",\n        \"calendar.availableCars\": \"Available Cars\",\n        // Active Bookings\n        \"activeBookings.title\": \"Active Bookings\",\n        \"activeBookings.subtitle\": \"Compact view of all rental bookings\",\n        \"activeBookings.searchBookings\": \"Search bookings...\",\n        \"activeBookings.booking\": \"BOOKING\",\n        \"activeBookings.client\": \"CLIENT\",\n        \"activeBookings.car\": \"CAR\",\n        \"activeBookings.dates\": \"DATES\",\n        \"activeBookings.location\": \"LOCATION\",\n        \"activeBookings.status\": \"STATUS\",\n        \"activeBookings.amount\": \"AMOUNT\",\n        \"activeBookings.actions\": \"ACTIONS\",\n        \"activeBookings.complete\": \"Complete\",\n        // Cars\n        \"cars.title\": \"Cars\",\n        \"cars.subtitle\": \"Compact fleet management\",\n        \"cars.allBrands\": \"All Brands\",\n        \"cars.allModels\": \"All Models\",\n        \"cars.searchCars\": \"Search cars...\",\n        \"cars.addCar\": \"Add Car\",\n        \"cars.details\": \"DETAILS\",\n        \"cars.rate\": \"RATE\",\n        // Damage Reports\n        \"damageReports.title\": \"Damage Reports\",\n        \"damageReports.subtitle\": \"Compact damage tracking system\",\n        \"damageReports.searchReports\": \"Search reports...\",\n        \"damageReports.newReport\": \"New Report\",\n        \"damageReports.report\": \"REPORT\",\n        \"damageReports.damage\": \"DAMAGE\",\n        \"damageReports.severity\": \"SEVERITY\",\n        \"damageReports.cost\": \"COST\",\n        \"damageReports.inRepair\": \"In Repair\",\n        \"damageReports.minor\": \"Minor\",\n        \"damageReports.moderate\": \"Moderate\",\n        \"damageReports.major\": \"Major\",\n        // Clients\n        \"clients.title\": \"Clients\",\n        \"clients.subtitle\": \"Manage your customer database and relationships\",\n        \"clients.searchClients\": \"Search clients by name, email, or phone...\",\n        \"clients.addClient\": \"Add Client\",\n        \"clients.totalClients\": \"Total Clients\",\n        \"clients.activeClients\": \"Active Clients\",\n        \"clients.vipClients\": \"VIP Clients\",\n        \"clients.avgRating\": \"Avg. Rating\",\n        \"clients.totalBookings\": \"Total Bookings\",\n        \"clients.totalSpent\": \"Total Spent\",\n        \"clients.lastBooking\": \"Last booking\",\n        \"clients.preferredCars\": \"Preferred Cars\",\n        \"clients.viewProfile\": \"View Profile\",\n        \"clients.editClient\": \"Edit Client\",\n        \"clients.newBooking\": \"New Booking\",\n        \"clients.joined\": \"Joined\",\n        \"clients.inactive\": \"Inactive\",\n        \"clients.vip\": \"VIP\",\n        // Wallet\n        \"wallet.title\": \"Wallet\",\n        \"wallet.subtitle\": \"Track your financial transactions and revenue\",\n        \"wallet.totalBalance\": \"Total Balance\",\n        \"wallet.thisMonthIncome\": \"This Month Income\",\n        \"wallet.pendingPayments\": \"Pending Payments\",\n        \"wallet.totalExpenses\": \"Total Expenses\",\n        \"wallet.recentTransactions\": \"Recent Transactions\",\n        \"wallet.latestActivities\": \"Your latest financial activities\",\n        \"wallet.quickActions\": \"Quick Actions\",\n        \"wallet.addIncome\": \"Add Income\",\n        \"wallet.recordExpense\": \"Record Expense\",\n        \"wallet.schedulePayment\": \"Schedule Payment\",\n        \"wallet.monthlySummary\": \"Monthly Summary\",\n        \"wallet.totalIncome\": \"Total Income\",\n        \"wallet.netProfit\": \"Net Profit\",\n        \"wallet.export\": \"Export\",\n        // Profile & Settings\n        \"profile.profile\": \"Profile\",\n        \"profile.settings\": \"Settings\",\n        \"profile.users\": \"Users\",\n        \"profile.logout\": \"Logout\",\n        \"profile.notifications\": \"Notifications\",\n        \"profile.administrator\": \"Administrator\",\n        // Notifications\n        \"notifications.title\": \"Notifications\",\n        \"notifications.newBookingRequest\": \"New booking request\",\n        \"notifications.bookingEnding\": \"Booking ending soon\",\n        \"notifications.bookingEnded\": \"Booking has ended\",\n        \"notifications.carMaintenanceDue\": \"Car maintenance due\",\n        \"notifications.paymentReceived\": \"Payment received\",\n        \"notifications.damageReported\": \"Damage reported\",\n        \"notifications.markAllRead\": \"Mark all as read\",\n        \"notifications.noNotifications\": \"No notifications\",\n        // Time\n        \"time.now\": \"now\",\n        \"time.minutesAgo\": \"minutes ago\",\n        \"time.hoursAgo\": \"hours ago\",\n        \"time.daysAgo\": \"days ago\",\n        \"time.days\": \"days\",\n        \"time.hours\": \"hours\",\n        // Status\n        \"status.available\": \"Available\",\n        \"status.rented\": \"Rented\",\n        \"status.maintenance\": \"Maintenance\",\n        \"status.active\": \"Active\",\n        \"status.confirmed\": \"Confirmed\",\n        \"status.pending\": \"Pending\",\n        \"status.completed\": \"Completed\",\n        \"status.cancelled\": \"Cancelled\",\n        \"status.paid\": \"Paid\",\n        \"status.overdue\": \"Overdue\",\n        // Months\n        \"month.january\": \"January\",\n        \"month.february\": \"February\",\n        \"month.march\": \"March\",\n        \"month.april\": \"April\",\n        \"month.may\": \"May\",\n        \"month.june\": \"June\",\n        \"month.july\": \"July\",\n        \"month.august\": \"August\",\n        \"month.september\": \"September\",\n        \"month.october\": \"October\",\n        \"month.november\": \"November\",\n        \"month.december\": \"December\",\n        // Days\n        \"day.sunday\": \"Sunday\",\n        \"day.monday\": \"Monday\",\n        \"day.tuesday\": \"Tuesday\",\n        \"day.wednesday\": \"Wednesday\",\n        \"day.thursday\": \"Thursday\",\n        \"day.friday\": \"Friday\",\n        \"day.saturday\": \"Saturday\",\n        \"day.sun\": \"Sun\",\n        \"day.mon\": \"Mon\",\n        \"day.tue\": \"Tue\",\n        \"day.wed\": \"Wed\",\n        \"day.thu\": \"Thu\",\n        \"day.fri\": \"Fri\",\n        \"day.sat\": \"Sat\"\n    }\n};\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction LanguageProvider(param) {\n    let { children } = param;\n    _s();\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"sq\") // Albanian as default\n    ;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            const savedLanguage = localStorage.getItem(\"language\");\n            if (savedLanguage && (savedLanguage === \"en\" || savedLanguage === \"sq\")) {\n                setLanguage(savedLanguage);\n            }\n        }\n    }[\"LanguageProvider.useEffect\"], []);\n    const handleSetLanguage = (lang)=>{\n        setLanguage(lang);\n        localStorage.setItem(\"language\", lang);\n    };\n    const t = (key)=>{\n        return translations[language][key] || key;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            setLanguage: handleSetLanguage,\n            t\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/language-provider.tsx\",\n        lineNumber: 516,\n        columnNumber: 5\n    }, this);\n}\n_s(LanguageProvider, \"MN6h2bbf5G9m+7EzYyRHv5L4k74=\");\n_c = LanguageProvider;\nfunction useLanguage() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        throw new Error(\"useLanguage must be used within a LanguageProvider\");\n    }\n    return context;\n}\n_s1(useLanguage, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"LanguageProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/language-provider.tsx\n"));

/***/ })

});