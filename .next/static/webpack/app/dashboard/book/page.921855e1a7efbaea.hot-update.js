"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/book/page",{

/***/ "(app-pages-browser)/./app/dashboard/book/page.tsx":
/*!*************************************!*\
  !*** ./app/dashboard/book/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BookPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_dashboard_header__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard-header */ \"(app-pages-browser)/./components/dashboard-header.tsx\");\n/* harmony import */ var _components_language_provider__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/language-provider */ \"(app-pages-browser)/./components/language-provider.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/fuel.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction BookPage() {\n    _s();\n    const { t } = (0,_components_language_provider__WEBPACK_IMPORTED_MODULE_8__.useLanguage)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)({\n        clientName: \"\",\n        clientPhone: \"\",\n        carId: \"\",\n        startDate: \"\",\n        endDate: \"\",\n        notes: \"\"\n    });\n    const [carSearch, setCarSearch] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(\"\");\n    const [showCarDropdown, setShowCarDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [selectedBrand, setSelectedBrand] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(\"all\");\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(\"all\");\n    const carBrands = {\n        BMW: \"https://logos-world.net/wp-content/uploads/2020/03/BMW-Logo.png\",\n        Mercedes: \"https://logos-world.net/wp-content/uploads/2020/04/Mercedes-Logo.png\",\n        Audi: \"https://logos-world.net/wp-content/uploads/2020/04/Audi-Logo.png\",\n        Toyota: \"https://logos-world.net/wp-content/uploads/2020/03/Toyota-Logo.png\",\n        Volkswagen: \"https://logos-world.net/wp-content/uploads/2020/04/Volkswagen-Logo.png\",\n        Ford: \"https://logos-world.net/wp-content/uploads/2020/04/Ford-Logo.png\",\n        Tesla: \"https://logos-world.net/wp-content/uploads/2020/04/Tesla-Logo.png\",\n        Porsche: \"https://logos-world.net/wp-content/uploads/2020/04/Porsche-Logo.png\"\n    };\n    const availableCars = [\n        {\n            id: \"1\",\n            make: \"BMW\",\n            model: \"X5\",\n            year: 2023,\n            rate: 120,\n            available: true,\n            type: \"luxury\",\n            fuelType: \"Petrol\",\n            seats: 5,\n            transmission: \"Auto\"\n        },\n        {\n            id: \"2\",\n            make: \"Mercedes\",\n            model: \"C-Class\",\n            year: 2022,\n            rate: 100,\n            available: true,\n            type: \"luxury\",\n            fuelType: \"Diesel\",\n            seats: 5,\n            transmission: \"Auto\"\n        },\n        {\n            id: \"3\",\n            make: \"Audi\",\n            model: \"A4\",\n            year: 2023,\n            rate: 110,\n            available: false,\n            type: \"sedan\",\n            fuelType: \"Petrol\",\n            seats: 5,\n            transmission: \"Manual\"\n        },\n        {\n            id: \"4\",\n            make: \"Toyota\",\n            model: \"Camry\",\n            year: 2022,\n            rate: 80,\n            available: true,\n            type: \"sedan\",\n            fuelType: \"Hybrid\",\n            seats: 5,\n            transmission: \"Auto\"\n        },\n        {\n            id: \"5\",\n            make: \"BMW\",\n            model: \"3 Series\",\n            year: 2023,\n            rate: 95,\n            available: true,\n            type: \"sedan\",\n            fuelType: \"Petrol\",\n            seats: 5,\n            transmission: \"Auto\"\n        },\n        {\n            id: \"6\",\n            make: \"Tesla\",\n            model: \"Model 3\",\n            year: 2023,\n            rate: 140,\n            available: true,\n            type: \"electric\",\n            fuelType: \"Electric\",\n            seats: 5,\n            transmission: \"Auto\"\n        },\n        {\n            id: \"7\",\n            make: \"Volkswagen\",\n            model: \"Golf\",\n            year: 2023,\n            rate: 70,\n            available: true,\n            type: \"compact\",\n            fuelType: \"Petrol\",\n            seats: 5,\n            transmission: \"Manual\"\n        },\n        {\n            id: \"8\",\n            make: \"Ford\",\n            model: \"Explorer\",\n            year: 2022,\n            rate: 95,\n            available: true,\n            type: \"suv\",\n            fuelType: \"Petrol\",\n            seats: 7,\n            transmission: \"Auto\"\n        },\n        {\n            id: \"9\",\n            make: \"Porsche\",\n            model: \"Cayenne\",\n            year: 2023,\n            rate: 200,\n            available: true,\n            type: \"luxury\",\n            fuelType: \"Petrol\",\n            seats: 5,\n            transmission: \"Auto\"\n        }\n    ];\n    const carTypes = [\n        {\n            id: \"all\",\n            name: t(\"common.all\"),\n            icon: _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: \"luxury\",\n            name: t(\"carType.luxury\"),\n            icon: _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            id: \"suv\",\n            name: t(\"carType.suv\"),\n            icon: _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            id: \"sedan\",\n            name: t(\"carType.sedan\"),\n            icon: _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            id: \"compact\",\n            name: t(\"carType.compact\"),\n            icon: _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        },\n        {\n            id: \"electric\",\n            name: t(\"carType.electric\"),\n            icon: _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        }\n    ];\n    const brands = [\n        \"all\",\n        ...Object.keys(carBrands)\n    ];\n    const filteredCars = availableCars.filter((car)=>{\n        const matchesSearch = car.make.toLowerCase().includes(carSearch.toLowerCase()) || car.model.toLowerCase().includes(carSearch.toLowerCase()) || \"\".concat(car.make, \" \").concat(car.model).toLowerCase().includes(carSearch.toLowerCase());\n        const matchesBrand = selectedBrand === \"all\" || car.make === selectedBrand;\n        const matchesType = selectedType === \"all\" || car.type === selectedType;\n        const isAvailable = car.available;\n        return matchesSearch && matchesBrand && matchesType && isAvailable;\n    });\n    const selectedCar = availableCars.find((car)=>car.id === formData.carId);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        console.log(\"Booking data:\", formData);\n    // Handle booking submission\n    };\n    const calculateTotal = ()=>{\n        if (formData.startDate && formData.endDate && formData.carId) {\n            const start = new Date(formData.startDate);\n            const end = new Date(formData.endDate);\n            const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));\n            const car = availableCars.find((c)=>c.id === formData.carId);\n            return days > 0 && car ? days * car.rate : 0;\n        }\n        return 0;\n    };\n    const selectCar = (car)=>{\n        setFormData({\n            ...formData,\n            carId: car.id\n        });\n        setCarSearch(\"\".concat(car.make, \" \").concat(car.model, \" \").concat(car.year));\n        setShowCarDropdown(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_header__WEBPACK_IMPORTED_MODULE_7__.DashboardHeader, {\n                title: t(\"nav.book\"),\n                subtitle: t(\"booking.title\"),\n                showActions: false\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-6 space-y-6 bg-gradient-to-br from-background to-muted/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                className: \"card-hover border-0 shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this),\n                                                t(\"booking.title\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"clientName\",\n                                                                    children: [\n                                                                        t(\"booking.clientName\"),\n                                                                        \" *\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                    id: \"clientName\",\n                                                                    value: formData.clientName,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            clientName: e.target.value\n                                                                        }),\n                                                                    placeholder: t(\"booking.clientName\"),\n                                                                    required: true,\n                                                                    className: \"h-12\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"clientPhone\",\n                                                                    children: [\n                                                                        t(\"booking.clientPhone\"),\n                                                                        \" *\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                    id: \"clientPhone\",\n                                                                    value: formData.clientPhone,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            clientPhone: e.target.value\n                                                                        }),\n                                                                    placeholder: t(\"booking.clientPhone\"),\n                                                                    required: true,\n                                                                    className: \"h-12\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            children: [\n                                                                t(\"booking.selectCar\"),\n                                                                \" *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                            lineNumber: 250,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                            value: carSearch,\n                                                                            onChange: (e)=>{\n                                                                                setCarSearch(e.target.value);\n                                                                                setShowCarDropdown(true);\n                                                                            },\n                                                                            onFocus: ()=>setShowCarDropdown(true),\n                                                                            placeholder: t(\"booking.searchCars\"),\n                                                                            className: \"pl-10 h-12\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                            lineNumber: 251,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-muted-foreground\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                lineNumber: 266,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-muted-foreground\",\n                                                                                children: [\n                                                                                    t(\"booking.filterByType\"),\n                                                                                    \":\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                lineNumber: 267,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            carTypes.map((type)=>{\n                                                                                const IconComponent = type.icon;\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    type: \"button\",\n                                                                                    variant: selectedType === type.id ? \"default\" : \"outline\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>setSelectedType(type.id),\n                                                                                    className: \"h-8 px-3 text-xs flex items-center gap-1.5\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                            className: \"h-3 w-3\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                            lineNumber: 279,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        type.name\n                                                                                    ]\n                                                                                }, type.id, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                    lineNumber: 271,\n                                                                                    columnNumber: 31\n                                                                                }, this);\n                                                                            })\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                        lineNumber: 265,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: [\n                                                                                t(\"booking.filterByBrand\"),\n                                                                                \":\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                            lineNumber: 288,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        brands.map((brand)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                type: \"button\",\n                                                                                variant: selectedBrand === brand ? \"default\" : \"outline\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>setSelectedBrand(brand),\n                                                                                className: \"h-8 px-3 text-xs flex items-center gap-2\",\n                                                                                children: [\n                                                                                    brand !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                        src: carBrands[brand] || \"/placeholder.svg\",\n                                                                                        alt: brand,\n                                                                                        className: \"w-4 h-4 object-contain\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                        lineNumber: 299,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    brand === \"all\" ? t(\"common.all\") : brand\n                                                                                ]\n                                                                            }, brand, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                lineNumber: 290,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 287,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border rounded-lg p-4 bg-muted/20\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium mb-3 flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                            lineNumber: 314,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        t(\"booking.availableCars\"),\n                                                                        \" (\",\n                                                                        filteredCars.length,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 313,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                filteredCars.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-3 max-h-60 overflow-y-auto\",\n                                                                    children: filteredCars.map((car)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md \".concat(formData.carId === car.id ? \"border-primary bg-primary/10\" : \"border-border hover:border-primary/50\"),\n                                                                            onClick: ()=>selectCar(car),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-between mb-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                                    src: carBrands[car.make] || \"/placeholder.svg\",\n                                                                                                    alt: car.make,\n                                                                                                    className: \"w-6 h-6 object-contain\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                    lineNumber: 332,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"font-medium text-sm\",\n                                                                                                            children: [\n                                                                                                                car.make,\n                                                                                                                \" \",\n                                                                                                                car.model,\n                                                                                                                \" \",\n                                                                                                                car.year\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                            lineNumber: 338,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                                                            variant: \"secondary\",\n                                                                                                            className: \"text-xs\",\n                                                                                                            children: t(\"carType.\".concat(car.type))\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                            lineNumber: 341,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                    lineNumber: 337,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                            lineNumber: 331,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        formData.carId === car.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                            className: \"h-4 w-4 text-primary\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                            lineNumber: 346,\n                                                                                            columnNumber: 63\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                    lineNumber: 330,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-between text-xs text-muted-foreground\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex items-center gap-1\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                                            className: \"h-3 w-3\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                            lineNumber: 352,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this),\n                                                                                                        car.fuelType\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                    lineNumber: 351,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex items-center gap-1\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                                            className: \"h-3 w-3\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                            lineNumber: 356,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this),\n                                                                                                        car.seats\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                    lineNumber: 355,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                            lineNumber: 350,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"font-bold text-primary\",\n                                                                                            children: [\n                                                                                                \"€\",\n                                                                                                car.rate,\n                                                                                                \"/\",\n                                                                                                t(\"time.days\")\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                            lineNumber: 360,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                    lineNumber: 349,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, car.id, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                            lineNumber: 321,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-center text-muted-foreground py-4\",\n                                                                    children: t(\"booking.noAvailableCars\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"startDate\",\n                                                                    children: [\n                                                                        t(\"booking.startDate\"),\n                                                                        \" *\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                    id: \"startDate\",\n                                                                    type: \"date\",\n                                                                    value: formData.startDate,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            startDate: e.target.value\n                                                                        }),\n                                                                    required: true,\n                                                                    className: \"h-12\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"endDate\",\n                                                                    children: [\n                                                                        t(\"booking.endDate\"),\n                                                                        \" *\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                    id: \"endDate\",\n                                                                    type: \"date\",\n                                                                    value: formData.endDate,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            endDate: e.target.value\n                                                                        }),\n                                                                    required: true,\n                                                                    className: \"h-12\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 387,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"notes\",\n                                                            children: t(\"booking.notes\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                            id: \"notes\",\n                                                            value: formData.notes,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    notes: e.target.value\n                                                                }),\n                                                            placeholder: t(\"booking.notes\"),\n                                                            className: \"min-h-[100px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    type: \"submit\",\n                                                    className: \"w-full h-12 modern-button gradient-bg text-lg font-semibold\",\n                                                    children: t(\"booking.create\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                className: \"card-hover border-0 shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                            children: t(\"booking.summary\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: formData.clientName || t(\"booking.clientName\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: selectedCar ? \"\".concat(selectedCar.make, \" \").concat(selectedCar.model, \" \").concat(selectedCar.year) : t(\"booking.selectCar\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: formData.startDate && formData.endDate ? \"\".concat(formData.startDate, \" - \").concat(formData.endDate) : t(\"booking.startDate\") + \" - \" + t(\"booking.endDate\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, this),\n                                            calculateTotal() > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: [\n                                                                        t(\"booking.dailyRate\"),\n                                                                        \":\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        \"€\",\n                                                                        selectedCar === null || selectedCar === void 0 ? void 0 : selectedCar.rate\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: [\n                                                                        t(\"booking.duration\"),\n                                                                        \":\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 454,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        Math.ceil((new Date(formData.endDate).getTime() - new Date(formData.startDate).getTime()) / (1000 * 60 * 60 * 24)),\n                                                                        \" \",\n                                                                        t(\"time.days\")\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center pt-2 border-t\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: [\n                                                                        t(\"booking.totalAmount\"),\n                                                                        \":\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold text-primary\",\n                                                                    children: [\n                                                                        \"€\",\n                                                                        calculateTotal()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                            lineNumber: 417,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n}\n_s(BookPage, \"lDHKySXaZLtAgH/XLjGzkLz5nn8=\", false, function() {\n    return [\n        _components_language_provider__WEBPACK_IMPORTED_MODULE_8__.useLanguage\n    ];\n});\n_c = BookPage;\nvar _c;\n$RefreshReg$(_c, \"BookPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/book/page.tsx\n"));

/***/ })

});