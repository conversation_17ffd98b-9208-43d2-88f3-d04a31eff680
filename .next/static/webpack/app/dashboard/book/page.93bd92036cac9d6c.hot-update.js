"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/book/page",{

/***/ "(app-pages-browser)/./app/dashboard/book/page.tsx":
/*!*************************************!*\
  !*** ./app/dashboard/book/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BookPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_dashboard_header__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard-header */ \"(app-pages-browser)/./components/dashboard-header.tsx\");\n/* harmony import */ var _components_language_provider__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/language-provider */ \"(app-pages-browser)/./components/language-provider.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Star,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Star,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Star,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Star,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Star,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Star,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Star,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Star,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Star,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Star,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Star,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/fuel.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Star,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Star,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Crown,Filter,Fuel,Minimize2,Search,Star,Truck,User,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction BookPage() {\n    _s();\n    const { t } = (0,_components_language_provider__WEBPACK_IMPORTED_MODULE_8__.useLanguage)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)({\n        clientName: \"\",\n        clientPhone: \"\",\n        carId: \"\",\n        startDate: \"\",\n        endDate: \"\",\n        notes: \"\"\n    });\n    const [carSearch, setCarSearch] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(\"\");\n    const [showCarDropdown, setShowCarDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [selectedBrand, setSelectedBrand] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(\"all\");\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(\"all\");\n    const carBrands = {\n        BMW: \"https://logos-world.net/wp-content/uploads/2020/03/BMW-Logo.png\",\n        Mercedes: \"https://logos-world.net/wp-content/uploads/2020/04/Mercedes-Logo.png\",\n        Audi: \"https://logos-world.net/wp-content/uploads/2020/04/Audi-Logo.png\",\n        Toyota: \"https://logos-world.net/wp-content/uploads/2020/03/Toyota-Logo.png\",\n        Volkswagen: \"https://logos-world.net/wp-content/uploads/2020/04/Volkswagen-Logo.png\",\n        Ford: \"https://logos-world.net/wp-content/uploads/2020/04/Ford-Logo.png\",\n        Tesla: \"https://logos-world.net/wp-content/uploads/2020/04/Tesla-Logo.png\",\n        Porsche: \"https://logos-world.net/wp-content/uploads/2020/04/Porsche-Logo.png\"\n    };\n    const availableCars = [\n        {\n            id: \"1\",\n            make: \"BMW\",\n            model: \"X5\",\n            year: 2023,\n            rate: 120,\n            available: true,\n            type: \"luxury\",\n            fuelType: \"Petrol\",\n            seats: 5,\n            transmission: \"Auto\"\n        },\n        {\n            id: \"2\",\n            make: \"Mercedes\",\n            model: \"C-Class\",\n            year: 2022,\n            rate: 100,\n            available: true,\n            type: \"luxury\",\n            fuelType: \"Diesel\",\n            seats: 5,\n            transmission: \"Auto\"\n        },\n        {\n            id: \"3\",\n            make: \"Audi\",\n            model: \"A4\",\n            year: 2023,\n            rate: 110,\n            available: false,\n            type: \"sedan\",\n            fuelType: \"Petrol\",\n            seats: 5,\n            transmission: \"Manual\"\n        },\n        {\n            id: \"4\",\n            make: \"Toyota\",\n            model: \"Camry\",\n            year: 2022,\n            rate: 80,\n            available: true,\n            type: \"sedan\",\n            fuelType: \"Hybrid\",\n            seats: 5,\n            transmission: \"Auto\"\n        },\n        {\n            id: \"5\",\n            make: \"BMW\",\n            model: \"3 Series\",\n            year: 2023,\n            rate: 95,\n            available: true,\n            type: \"sedan\",\n            fuelType: \"Petrol\",\n            seats: 5,\n            transmission: \"Auto\"\n        },\n        {\n            id: \"6\",\n            make: \"Tesla\",\n            model: \"Model 3\",\n            year: 2023,\n            rate: 140,\n            available: true,\n            type: \"electric\",\n            fuelType: \"Electric\",\n            seats: 5,\n            transmission: \"Auto\"\n        },\n        {\n            id: \"7\",\n            make: \"Volkswagen\",\n            model: \"Golf\",\n            year: 2023,\n            rate: 70,\n            available: true,\n            type: \"compact\",\n            fuelType: \"Petrol\",\n            seats: 5,\n            transmission: \"Manual\"\n        },\n        {\n            id: \"8\",\n            make: \"Ford\",\n            model: \"Explorer\",\n            year: 2022,\n            rate: 95,\n            available: true,\n            type: \"suv\",\n            fuelType: \"Petrol\",\n            seats: 7,\n            transmission: \"Auto\"\n        },\n        {\n            id: \"9\",\n            make: \"Porsche\",\n            model: \"Cayenne\",\n            year: 2023,\n            rate: 200,\n            available: true,\n            type: \"luxury\",\n            fuelType: \"Petrol\",\n            seats: 5,\n            transmission: \"Auto\"\n        }\n    ];\n    const carTypes = [\n        {\n            id: \"all\",\n            name: t(\"common.all\"),\n            icon: _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: \"luxury\",\n            name: t(\"carType.luxury\"),\n            icon: _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            id: \"suv\",\n            name: t(\"carType.suv\"),\n            icon: _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            id: \"sedan\",\n            name: t(\"carType.sedan\"),\n            icon: _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            id: \"compact\",\n            name: t(\"carType.compact\"),\n            icon: _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        },\n        {\n            id: \"electric\",\n            name: t(\"carType.electric\"),\n            icon: _barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        }\n    ];\n    const brands = [\n        \"all\",\n        ...Object.keys(carBrands)\n    ];\n    const filteredCars = availableCars.filter((car)=>{\n        const matchesSearch = car.make.toLowerCase().includes(carSearch.toLowerCase()) || car.model.toLowerCase().includes(carSearch.toLowerCase()) || \"\".concat(car.make, \" \").concat(car.model).toLowerCase().includes(carSearch.toLowerCase());\n        const matchesBrand = selectedBrand === \"all\" || car.make === selectedBrand;\n        const matchesType = selectedType === \"all\" || car.type === selectedType;\n        const isAvailable = car.available;\n        return matchesSearch && matchesBrand && matchesType && isAvailable;\n    });\n    const selectedCar = availableCars.find((car)=>car.id === formData.carId);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        console.log(\"Booking data:\", formData);\n    // Handle booking submission\n    };\n    const calculateTotal = ()=>{\n        if (formData.startDate && formData.endDate && formData.carId) {\n            const start = new Date(formData.startDate);\n            const end = new Date(formData.endDate);\n            const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));\n            const car = availableCars.find((c)=>c.id === formData.carId);\n            return days > 0 && car ? days * car.rate : 0;\n        }\n        return 0;\n    };\n    const selectCar = (car)=>{\n        setFormData({\n            ...formData,\n            carId: car.id\n        });\n        setCarSearch(\"\".concat(car.make, \" \").concat(car.model, \" \").concat(car.year));\n        setShowCarDropdown(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_header__WEBPACK_IMPORTED_MODULE_7__.DashboardHeader, {\n                title: t(\"nav.book\"),\n                subtitle: t(\"booking.title\"),\n                showActions: false\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-6 space-y-6 bg-gradient-to-br from-background to-muted/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                className: \"card-hover border-0 shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this),\n                                                t(\"booking.title\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"clientName\",\n                                                                    children: [\n                                                                        t(\"booking.clientName\"),\n                                                                        \" *\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                    id: \"clientName\",\n                                                                    value: formData.clientName,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            clientName: e.target.value\n                                                                        }),\n                                                                    placeholder: t(\"booking.clientName\"),\n                                                                    required: true,\n                                                                    className: \"h-12\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"clientPhone\",\n                                                                    children: [\n                                                                        t(\"booking.clientPhone\"),\n                                                                        \" *\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                    id: \"clientPhone\",\n                                                                    value: formData.clientPhone,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            clientPhone: e.target.value\n                                                                        }),\n                                                                    placeholder: t(\"booking.clientPhone\"),\n                                                                    required: true,\n                                                                    className: \"h-12\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            children: [\n                                                                t(\"booking.selectCar\"),\n                                                                \" *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                            lineNumber: 250,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                            value: carSearch,\n                                                                            onChange: (e)=>{\n                                                                                setCarSearch(e.target.value);\n                                                                                setShowCarDropdown(true);\n                                                                            },\n                                                                            onFocus: ()=>setShowCarDropdown(true),\n                                                                            placeholder: t(\"booking.searchCars\"),\n                                                                            className: \"pl-10 h-12\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                            lineNumber: 251,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-muted-foreground\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                lineNumber: 266,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-muted-foreground\",\n                                                                                children: [\n                                                                                    t(\"booking.filterByType\"),\n                                                                                    \":\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                lineNumber: 267,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            carTypes.map((type)=>{\n                                                                                const IconComponent = type.icon;\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    type: \"button\",\n                                                                                    variant: selectedType === type.id ? \"default\" : \"outline\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>setSelectedType(type.id),\n                                                                                    className: \"h-8 px-3 text-xs flex items-center gap-1.5\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                            className: \"h-3 w-3\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                            lineNumber: 279,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        type.name\n                                                                                    ]\n                                                                                }, type.id, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                    lineNumber: 271,\n                                                                                    columnNumber: 31\n                                                                                }, this);\n                                                                            })\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                        lineNumber: 265,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                    lineNumber: 289,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: [\n                                                                                        t(\"booking.filterByBrand\"),\n                                                                                        \":\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                    lineNumber: 290,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                            lineNumber: 288,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap gap-2\",\n                                                                            children: brands.map((brand)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    type: \"button\",\n                                                                                    variant: selectedBrand === brand ? \"default\" : \"outline\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>setSelectedBrand(brand),\n                                                                                    className: \"h-8 px-3 text-xs flex items-center gap-2\",\n                                                                                    children: [\n                                                                                        brand !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                            src: carBrands[brand] || \"/placeholder.svg\",\n                                                                                            alt: brand,\n                                                                                            className: \"w-4 h-4 object-contain\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                            lineNumber: 303,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        brand === \"all\" ? t(\"common.all\") : brand\n                                                                                    ]\n                                                                                }, brand, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                    lineNumber: 294,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                            lineNumber: 292,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 287,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border rounded-lg p-4 bg-muted/20\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium mb-3 flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                            lineNumber: 319,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        t(\"booking.availableCars\"),\n                                                                        \" (\",\n                                                                        filteredCars.length,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                filteredCars.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-3 max-h-60 overflow-y-auto\",\n                                                                    children: filteredCars.map((car)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md \".concat(formData.carId === car.id ? \"border-primary bg-primary/10\" : \"border-border hover:border-primary/50\"),\n                                                                            onClick: ()=>selectCar(car),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-between mb-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                                    src: carBrands[car.make] || \"/placeholder.svg\",\n                                                                                                    alt: car.make,\n                                                                                                    className: \"w-6 h-6 object-contain\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                    lineNumber: 337,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"font-medium text-sm\",\n                                                                                                            children: [\n                                                                                                                car.make,\n                                                                                                                \" \",\n                                                                                                                car.model,\n                                                                                                                \" \",\n                                                                                                                car.year\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                            lineNumber: 343,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                                                            variant: \"secondary\",\n                                                                                                            className: \"text-xs\",\n                                                                                                            children: t(\"carType.\".concat(car.type))\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                            lineNumber: 346,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                    lineNumber: 342,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                            lineNumber: 336,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        formData.carId === car.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                            className: \"h-4 w-4 text-primary\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                            lineNumber: 351,\n                                                                                            columnNumber: 63\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                    lineNumber: 335,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-between text-xs text-muted-foreground\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex items-center gap-1\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                                            className: \"h-3 w-3\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                            lineNumber: 357,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this),\n                                                                                                        car.fuelType\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                    lineNumber: 356,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex items-center gap-1\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                                            className: \"h-3 w-3\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                            lineNumber: 361,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this),\n                                                                                                        car.seats\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                    lineNumber: 360,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                            lineNumber: 355,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"font-bold text-primary\",\n                                                                                            children: [\n                                                                                                \"€\",\n                                                                                                car.rate,\n                                                                                                \"/\",\n                                                                                                t(\"time.days\")\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                            lineNumber: 365,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                    lineNumber: 354,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, car.id, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                            lineNumber: 326,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-center text-muted-foreground py-4\",\n                                                                    children: t(\"booking.noAvailableCars\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 373,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"startDate\",\n                                                                    children: [\n                                                                        t(\"booking.startDate\"),\n                                                                        \" *\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                    id: \"startDate\",\n                                                                    type: \"date\",\n                                                                    value: formData.startDate,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            startDate: e.target.value\n                                                                        }),\n                                                                    required: true,\n                                                                    className: \"h-12\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"endDate\",\n                                                                    children: [\n                                                                        t(\"booking.endDate\"),\n                                                                        \" *\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                    id: \"endDate\",\n                                                                    type: \"date\",\n                                                                    value: formData.endDate,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            endDate: e.target.value\n                                                                        }),\n                                                                    required: true,\n                                                                    className: \"h-12\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"notes\",\n                                                            children: t(\"booking.notes\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                            id: \"notes\",\n                                                            value: formData.notes,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    notes: e.target.value\n                                                                }),\n                                                            placeholder: t(\"booking.notes\"),\n                                                            className: \"min-h-[100px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    type: \"submit\",\n                                                    className: \"w-full h-12 modern-button gradient-bg text-lg font-semibold\",\n                                                    children: t(\"booking.create\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                className: \"card-hover border-0 shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                            children: t(\"booking.summary\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: formData.clientName || t(\"booking.clientName\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: selectedCar ? \"\".concat(selectedCar.make, \" \").concat(selectedCar.model, \" \").concat(selectedCar.year) : t(\"booking.selectCar\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Crown_Filter_Fuel_Minimize2_Search_Star_Truck_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: formData.startDate && formData.endDate ? \"\".concat(formData.startDate, \" - \").concat(formData.endDate) : t(\"booking.startDate\") + \" - \" + t(\"booking.endDate\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this),\n                                            calculateTotal() > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: [\n                                                                        t(\"booking.dailyRate\"),\n                                                                        \":\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        \"€\",\n                                                                        selectedCar === null || selectedCar === void 0 ? void 0 : selectedCar.rate\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: [\n                                                                        t(\"booking.duration\"),\n                                                                        \":\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        Math.ceil((new Date(formData.endDate).getTime() - new Date(formData.startDate).getTime()) / (1000 * 60 * 60 * 24)),\n                                                                        \" \",\n                                                                        t(\"time.days\")\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 460,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center pt-2 border-t\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: [\n                                                                        t(\"booking.totalAmount\"),\n                                                                        \":\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold text-primary\",\n                                                                    children: [\n                                                                        \"€\",\n                                                                        calculateTotal()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n}\n_s(BookPage, \"lDHKySXaZLtAgH/XLjGzkLz5nn8=\", false, function() {\n    return [\n        _components_language_provider__WEBPACK_IMPORTED_MODULE_8__.useLanguage\n    ];\n});\n_c = BookPage;\nvar _c;\n$RefreshReg$(_c, \"BookPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9kYXNoYm9hcmQvYm9vay9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSStFO0FBQ2hDO0FBQ0Y7QUFDQTtBQUNNO0FBQ047QUFDa0I7QUFDSDtBQUN3RTtBQUNwRztBQUVqQixTQUFTMEI7O0lBQ3RCLE1BQU0sRUFBRUMsQ0FBQyxFQUFFLEdBQUdqQiwwRUFBV0E7SUFDekIsTUFBTSxDQUFDa0IsVUFBVUMsWUFBWSxHQUFHSiwrQ0FBUUEsQ0FBQztRQUN2Q0ssWUFBWTtRQUNaQyxhQUFhO1FBQ2JDLE9BQU87UUFDUEMsV0FBVztRQUNYQyxTQUFTO1FBQ1RDLE9BQU87SUFDVDtJQUNBLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHWiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNhLGlCQUFpQkMsbUJBQW1CLEdBQUdkLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ2UsZUFBZUMsaUJBQWlCLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUNpQixjQUFjQyxnQkFBZ0IsR0FBR2xCLCtDQUFRQSxDQUFDO0lBRWpELE1BQU1tQixZQUFZO1FBQ2hCQyxLQUFLO1FBQ0xDLFVBQVU7UUFDVkMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLFlBQVk7UUFDWkMsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLFNBQVM7SUFDWDtJQUVBLE1BQU1DLGdCQUFnQjtRQUNwQjtZQUNFQyxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsV0FBVztZQUNYQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsT0FBTztZQUNQQyxjQUFjO1FBQ2hCO1FBQ0E7WUFDRVQsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLE9BQU87WUFDUEMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFdBQVc7WUFDWEMsTUFBTTtZQUNOQyxVQUFVO1lBQ1ZDLE9BQU87WUFDUEMsY0FBYztRQUNoQjtRQUNBO1lBQ0VULElBQUk7WUFDSkMsTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxXQUFXO1lBQ1hDLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxPQUFPO1lBQ1BDLGNBQWM7UUFDaEI7UUFDQTtZQUNFVCxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsV0FBVztZQUNYQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsT0FBTztZQUNQQyxjQUFjO1FBQ2hCO1FBQ0E7WUFDRVQsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLE9BQU87WUFDUEMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFdBQVc7WUFDWEMsTUFBTTtZQUNOQyxVQUFVO1lBQ1ZDLE9BQU87WUFDUEMsY0FBYztRQUNoQjtRQUNBO1lBQ0VULElBQUk7WUFDSkMsTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxXQUFXO1lBQ1hDLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxPQUFPO1lBQ1BDLGNBQWM7UUFDaEI7UUFDQTtZQUNFVCxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsV0FBVztZQUNYQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsT0FBTztZQUNQQyxjQUFjO1FBQ2hCO1FBQ0E7WUFDRVQsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLE9BQU87WUFDUEMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFdBQVc7WUFDWEMsTUFBTTtZQUNOQyxVQUFVO1lBQ1ZDLE9BQU87WUFDUEMsY0FBYztRQUNoQjtRQUNBO1lBQ0VULElBQUk7WUFDSkMsTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxXQUFXO1lBQ1hDLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxPQUFPO1lBQ1BDLGNBQWM7UUFDaEI7S0FDRDtJQUVELE1BQU1DLFdBQVc7UUFDZjtZQUFFVixJQUFJO1lBQU9XLE1BQU10QyxFQUFFO1lBQWV1QyxNQUFNakQscUtBQU1BO1FBQUM7UUFDakQ7WUFBRXFDLElBQUk7WUFBVVcsTUFBTXRDLEVBQUU7WUFBbUJ1QyxNQUFNOUMscUtBQUtBO1FBQUM7UUFDdkQ7WUFBRWtDLElBQUk7WUFBT1csTUFBTXRDLEVBQUU7WUFBZ0J1QyxNQUFNN0MscUtBQUtBO1FBQUM7UUFDakQ7WUFBRWlDLElBQUk7WUFBU1csTUFBTXRDLEVBQUU7WUFBa0J1QyxNQUFNcEQscUtBQUdBO1FBQUM7UUFDbkQ7WUFBRXdDLElBQUk7WUFBV1csTUFBTXRDLEVBQUU7WUFBb0J1QyxNQUFNNUMscUtBQVNBO1FBQUM7UUFDN0Q7WUFBRWdDLElBQUk7WUFBWVcsTUFBTXRDLEVBQUU7WUFBcUJ1QyxNQUFNM0MscUtBQUdBO1FBQUM7S0FDMUQ7SUFFRCxNQUFNNEMsU0FBUztRQUFDO1dBQVVDLE9BQU9DLElBQUksQ0FBQ3pCO0tBQVc7SUFFakQsTUFBTTBCLGVBQWVqQixjQUFja0IsTUFBTSxDQUFDLENBQUNDO1FBQ3pDLE1BQU1DLGdCQUNKRCxJQUFJakIsSUFBSSxDQUFDbUIsV0FBVyxHQUFHQyxRQUFRLENBQUN2QyxVQUFVc0MsV0FBVyxPQUNyREYsSUFBSWhCLEtBQUssQ0FBQ2tCLFdBQVcsR0FBR0MsUUFBUSxDQUFDdkMsVUFBVXNDLFdBQVcsT0FDdEQsR0FBZUYsT0FBWkEsSUFBSWpCLElBQUksRUFBQyxLQUFhLE9BQVZpQixJQUFJaEIsS0FBSyxFQUFHa0IsV0FBVyxHQUFHQyxRQUFRLENBQUN2QyxVQUFVc0MsV0FBVztRQUV6RSxNQUFNRSxlQUFlcEMsa0JBQWtCLFNBQVNnQyxJQUFJakIsSUFBSSxLQUFLZjtRQUM3RCxNQUFNcUMsY0FBY25DLGlCQUFpQixTQUFTOEIsSUFBSVosSUFBSSxLQUFLbEI7UUFDM0QsTUFBTW9DLGNBQWNOLElBQUliLFNBQVM7UUFFakMsT0FBT2MsaUJBQWlCRyxnQkFBZ0JDLGVBQWVDO0lBQ3pEO0lBRUEsTUFBTUMsY0FBYzFCLGNBQWMyQixJQUFJLENBQUMsQ0FBQ1IsTUFBUUEsSUFBSWxCLEVBQUUsS0FBSzFCLFNBQVNJLEtBQUs7SUFFekUsTUFBTWlELGVBQWUsQ0FBQ0M7UUFDcEJBLEVBQUVDLGNBQWM7UUFDaEJDLFFBQVFDLEdBQUcsQ0FBQyxpQkFBaUJ6RDtJQUM3Qiw0QkFBNEI7SUFDOUI7SUFFQSxNQUFNMEQsaUJBQWlCO1FBQ3JCLElBQUkxRCxTQUFTSyxTQUFTLElBQUlMLFNBQVNNLE9BQU8sSUFBSU4sU0FBU0ksS0FBSyxFQUFFO1lBQzVELE1BQU11RCxRQUFRLElBQUlDLEtBQUs1RCxTQUFTSyxTQUFTO1lBQ3pDLE1BQU13RCxNQUFNLElBQUlELEtBQUs1RCxTQUFTTSxPQUFPO1lBQ3JDLE1BQU13RCxPQUFPQyxLQUFLQyxJQUFJLENBQUMsQ0FBQ0gsSUFBSUksT0FBTyxLQUFLTixNQUFNTSxPQUFPLEVBQUMsSUFBTSxRQUFPLEtBQUssS0FBSyxFQUFDO1lBQzlFLE1BQU1yQixNQUFNbkIsY0FBYzJCLElBQUksQ0FBQyxDQUFDYyxJQUFNQSxFQUFFeEMsRUFBRSxLQUFLMUIsU0FBU0ksS0FBSztZQUM3RCxPQUFPMEQsT0FBTyxLQUFLbEIsTUFBTWtCLE9BQU9sQixJQUFJZCxJQUFJLEdBQUc7UUFDN0M7UUFDQSxPQUFPO0lBQ1Q7SUFFQSxNQUFNcUMsWUFBWSxDQUFDdkI7UUFDakIzQyxZQUFZO1lBQUUsR0FBR0QsUUFBUTtZQUFFSSxPQUFPd0MsSUFBSWxCLEVBQUU7UUFBQztRQUN6Q2pCLGFBQWEsR0FBZW1DLE9BQVpBLElBQUlqQixJQUFJLEVBQUMsS0FBZ0JpQixPQUFiQSxJQUFJaEIsS0FBSyxFQUFDLEtBQVksT0FBVGdCLElBQUlmLElBQUk7UUFDakRsQixtQkFBbUI7SUFDckI7SUFFQSxxQkFDRSw4REFBQ3lEO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDeEYseUVBQWVBO2dCQUFDeUYsT0FBT3ZFLEVBQUU7Z0JBQWF3RSxVQUFVeEUsRUFBRTtnQkFBa0J5RSxhQUFhOzs7Ozs7MEJBRWxGLDhEQUFDSjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ2pHLHFEQUFJQTtnQ0FBQ2lHLFdBQVU7O2tEQUNkLDhEQUFDL0YsMkRBQVVBO2tEQUNULDRFQUFDQywwREFBU0E7NENBQUM4RixXQUFVOzs4REFDbkIsOERBQUN0RixxS0FBUUE7b0RBQUNzRixXQUFVOzs7Ozs7Z0RBQ25CdEUsRUFBRTs7Ozs7Ozs7Ozs7O2tEQUdQLDhEQUFDMUIsNERBQVdBO2tEQUNWLDRFQUFDb0c7NENBQUtDLFVBQVVyQjs0Q0FBY2dCLFdBQVU7OzhEQUN0Qyw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUMzRix1REFBS0E7b0VBQUNpRyxTQUFROzt3RUFBYzVFLEVBQUU7d0VBQXNCOzs7Ozs7OzhFQUNyRCw4REFBQ3RCLHVEQUFLQTtvRUFDSmlELElBQUc7b0VBQ0hrRCxPQUFPNUUsU0FBU0UsVUFBVTtvRUFDMUIyRSxVQUFVLENBQUN2QixJQUFNckQsWUFBWTs0RUFBRSxHQUFHRCxRQUFROzRFQUFFRSxZQUFZb0QsRUFBRXdCLE1BQU0sQ0FBQ0YsS0FBSzt3RUFBQztvRUFDdkVHLGFBQWFoRixFQUFFO29FQUNmaUYsUUFBUTtvRUFDUlgsV0FBVTs7Ozs7Ozs7Ozs7O3NFQUdkLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUMzRix1REFBS0E7b0VBQUNpRyxTQUFROzt3RUFBZTVFLEVBQUU7d0VBQXVCOzs7Ozs7OzhFQUN2RCw4REFBQ3RCLHVEQUFLQTtvRUFDSmlELElBQUc7b0VBQ0hrRCxPQUFPNUUsU0FBU0csV0FBVztvRUFDM0IwRSxVQUFVLENBQUN2QixJQUFNckQsWUFBWTs0RUFBRSxHQUFHRCxRQUFROzRFQUFFRyxhQUFhbUQsRUFBRXdCLE1BQU0sQ0FBQ0YsS0FBSzt3RUFBQztvRUFDeEVHLGFBQWFoRixFQUFFO29FQUNmaUYsUUFBUTtvRUFDUlgsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQU1oQiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDM0YsdURBQUtBOztnRUFBRXFCLEVBQUU7Z0VBQXFCOzs7Ozs7O3NFQUcvQiw4REFBQ3FFOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDbEYscUtBQU1BOzRFQUFDa0YsV0FBVTs7Ozs7O3NGQUNsQiw4REFBQzVGLHVEQUFLQTs0RUFDSm1HLE9BQU9wRTs0RUFDUHFFLFVBQVUsQ0FBQ3ZCO2dGQUNUN0MsYUFBYTZDLEVBQUV3QixNQUFNLENBQUNGLEtBQUs7Z0ZBQzNCakUsbUJBQW1COzRFQUNyQjs0RUFDQXNFLFNBQVMsSUFBTXRFLG1CQUFtQjs0RUFDbENvRSxhQUFhaEYsRUFBRTs0RUFDZnNFLFdBQVU7Ozs7Ozs7Ozs7Ozs4RUFLZCw4REFBQ0Q7b0VBQUlDLFdBQVU7OEVBQ2IsNEVBQUNEO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ2hGLHFLQUFNQTtnRkFBQ2dGLFdBQVU7Ozs7OzswRkFDbEIsOERBQUNhO2dGQUFLYixXQUFVOztvRkFBaUN0RSxFQUFFO29GQUF3Qjs7Ozs7Ozs0RUFDMUVxQyxTQUFTK0MsR0FBRyxDQUFDLENBQUNuRDtnRkFDYixNQUFNb0QsZ0JBQWdCcEQsS0FBS00sSUFBSTtnRkFDL0IscUJBQ0UsOERBQUM5RCx5REFBTUE7b0ZBRUx3RCxNQUFLO29GQUNMcUQsU0FBU3ZFLGlCQUFpQmtCLEtBQUtOLEVBQUUsR0FBRyxZQUFZO29GQUNoRDRELE1BQUs7b0ZBQ0xDLFNBQVMsSUFBTXhFLGdCQUFnQmlCLEtBQUtOLEVBQUU7b0ZBQ3RDMkMsV0FBVTs7c0dBRVYsOERBQUNlOzRGQUFjZixXQUFVOzs7Ozs7d0ZBQ3hCckMsS0FBS0ssSUFBSTs7bUZBUkxMLEtBQUtOLEVBQUU7Ozs7OzRFQVdsQjs7Ozs7Ozs7Ozs7OzhFQUlKLDhEQUFDMEM7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUN6RSxxS0FBSUE7b0ZBQUN5RSxXQUFVOzs7Ozs7OEZBQ2hCLDhEQUFDYTtvRkFBS2IsV0FBVTs7d0ZBQWlDdEUsRUFBRTt3RkFBeUI7Ozs7Ozs7Ozs7Ozs7c0ZBRTlFLDhEQUFDcUU7NEVBQUlDLFdBQVU7c0ZBQ1o5QixPQUFPNEMsR0FBRyxDQUFDLENBQUNLLHNCQUNYLDhEQUFDaEgseURBQU1BO29GQUVMd0QsTUFBSztvRkFDTHFELFNBQVN6RSxrQkFBa0I0RSxRQUFRLFlBQVk7b0ZBQy9DRixNQUFLO29GQUNMQyxTQUFTLElBQU0xRSxpQkFBaUIyRTtvRkFDaENuQixXQUFVOzt3RkFFVG1CLFVBQVUsdUJBQ1QsOERBQUNDOzRGQUNDQyxLQUFLMUUsU0FBUyxDQUFDd0UsTUFBZ0MsSUFBSTs0RkFDbkRHLEtBQUtIOzRGQUNMbkIsV0FBVTs7Ozs7O3dGQUdibUIsVUFBVSxRQUFRekYsRUFBRSxnQkFBZ0J5Rjs7bUZBZGhDQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRUFzQmYsOERBQUNwQjs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUN1QjtvRUFBR3ZCLFdBQVU7O3NGQUNaLDhEQUFDbkYscUtBQUdBOzRFQUFDbUYsV0FBVTs7Ozs7O3dFQUNkdEUsRUFBRTt3RUFBeUI7d0VBQUcyQyxhQUFhbUQsTUFBTTt3RUFBQzs7Ozs7OztnRUFHcERuRCxhQUFhbUQsTUFBTSxHQUFHLGtCQUNyQiw4REFBQ3pCO29FQUFJQyxXQUFVOzhFQUNaM0IsYUFBYXlDLEdBQUcsQ0FBQyxDQUFDdkMsb0JBQ2pCLDhEQUFDd0I7NEVBRUNDLFdBQVcsdUVBSVYsT0FIQ3JFLFNBQVNJLEtBQUssS0FBS3dDLElBQUlsQixFQUFFLEdBQ3JCLGlDQUNBOzRFQUVONkQsU0FBUyxJQUFNcEIsVUFBVXZCOzs4RkFFekIsOERBQUN3QjtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUNEOzRGQUFJQyxXQUFVOzs4R0FDYiw4REFBQ29CO29HQUNDQyxLQUFLMUUsU0FBUyxDQUFDNEIsSUFBSWpCLElBQUksQ0FBMkIsSUFBSTtvR0FDdERnRSxLQUFLL0MsSUFBSWpCLElBQUk7b0dBQ2IwQyxXQUFVOzs7Ozs7OEdBRVosOERBQUNEOztzSEFDQyw4REFBQzBCOzRHQUFFekIsV0FBVTs7Z0hBQ1Z6QixJQUFJakIsSUFBSTtnSEFBQztnSEFBRWlCLElBQUloQixLQUFLO2dIQUFDO2dIQUFFZ0IsSUFBSWYsSUFBSTs7Ozs7OztzSEFFbEMsOERBQUNqRCx1REFBS0E7NEdBQUN5RyxTQUFROzRHQUFZaEIsV0FBVTtzSEFDbEN0RSxFQUFFLFdBQW9CLE9BQVQ2QyxJQUFJWixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7d0ZBSTNCaEMsU0FBU0ksS0FBSyxLQUFLd0MsSUFBSWxCLEVBQUUsa0JBQUksOERBQUN0QyxxS0FBS0E7NEZBQUNpRixXQUFVOzs7Ozs7Ozs7Ozs7OEZBR2pELDhEQUFDRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUNEOzRGQUFJQyxXQUFVOzs4R0FDYiw4REFBQ0Q7b0dBQUlDLFdBQVU7O3NIQUNiLDhEQUFDL0UscUtBQUlBOzRHQUFDK0UsV0FBVTs7Ozs7O3dHQUNmekIsSUFBSVgsUUFBUTs7Ozs7Ozs4R0FFZiw4REFBQ21DO29HQUFJQyxXQUFVOztzSEFDYiw4REFBQzlFLHFLQUFLQTs0R0FBQzhFLFdBQVU7Ozs7Ozt3R0FDaEJ6QixJQUFJVixLQUFLOzs7Ozs7Ozs7Ozs7O3NHQUdkLDhEQUFDZ0Q7NEZBQUtiLFdBQVU7O2dHQUF5QjtnR0FDckN6QixJQUFJZCxJQUFJO2dHQUFDO2dHQUFFL0IsRUFBRTs7Ozs7Ozs7Ozs7Ozs7MkVBdkNkNkMsSUFBSWxCLEVBQUU7Ozs7Ozs7Ozt5RkE4Q2pCLDhEQUFDb0U7b0VBQUV6QixXQUFVOzhFQUEwQ3RFLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFLL0QsOERBQUNxRTtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQzNGLHVEQUFLQTtvRUFBQ2lHLFNBQVE7O3dFQUFhNUUsRUFBRTt3RUFBcUI7Ozs7Ozs7OEVBQ25ELDhEQUFDdEIsdURBQUtBO29FQUNKaUQsSUFBRztvRUFDSE0sTUFBSztvRUFDTDRDLE9BQU81RSxTQUFTSyxTQUFTO29FQUN6QndFLFVBQVUsQ0FBQ3ZCLElBQU1yRCxZQUFZOzRFQUFFLEdBQUdELFFBQVE7NEVBQUVLLFdBQVdpRCxFQUFFd0IsTUFBTSxDQUFDRixLQUFLO3dFQUFDO29FQUN0RUksUUFBUTtvRUFDUlgsV0FBVTs7Ozs7Ozs7Ozs7O3NFQUdkLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUMzRix1REFBS0E7b0VBQUNpRyxTQUFROzt3RUFBVzVFLEVBQUU7d0VBQW1COzs7Ozs7OzhFQUMvQyw4REFBQ3RCLHVEQUFLQTtvRUFDSmlELElBQUc7b0VBQ0hNLE1BQUs7b0VBQ0w0QyxPQUFPNUUsU0FBU00sT0FBTztvRUFDdkJ1RSxVQUFVLENBQUN2QixJQUFNckQsWUFBWTs0RUFBRSxHQUFHRCxRQUFROzRFQUFFTSxTQUFTZ0QsRUFBRXdCLE1BQU0sQ0FBQ0YsS0FBSzt3RUFBQztvRUFDcEVJLFFBQVE7b0VBQ1JYLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFLaEIsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQzNGLHVEQUFLQTs0REFBQ2lHLFNBQVE7c0VBQVM1RSxFQUFFOzs7Ozs7c0VBQzFCLDhEQUFDcEIsNkRBQVFBOzREQUNQK0MsSUFBRzs0REFDSGtELE9BQU81RSxTQUFTTyxLQUFLOzREQUNyQnNFLFVBQVUsQ0FBQ3ZCLElBQU1yRCxZQUFZO29FQUFFLEdBQUdELFFBQVE7b0VBQUVPLE9BQU8rQyxFQUFFd0IsTUFBTSxDQUFDRixLQUFLO2dFQUFDOzREQUNsRUcsYUFBYWhGLEVBQUU7NERBQ2ZzRSxXQUFVOzs7Ozs7Ozs7Ozs7OERBSWQsOERBQUM3Rix5REFBTUE7b0RBQUN3RCxNQUFLO29EQUFTcUMsV0FBVTs4REFDN0J0RSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU9iLDhEQUFDcUU7c0NBQ0MsNEVBQUNoRyxxREFBSUE7Z0NBQUNpRyxXQUFVOztrREFDZCw4REFBQy9GLDJEQUFVQTtrREFDVCw0RUFBQ0MsMERBQVNBO3NEQUFFd0IsRUFBRTs7Ozs7Ozs7Ozs7a0RBRWhCLDhEQUFDMUIsNERBQVdBO3dDQUFDZ0csV0FBVTs7MERBQ3JCLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNwRixxS0FBSUE7d0RBQUNvRixXQUFVOzs7Ozs7a0VBQ2hCLDhEQUFDYTt3REFBS2IsV0FBVTtrRUFBV3JFLFNBQVNFLFVBQVUsSUFBSUgsRUFBRTs7Ozs7Ozs7Ozs7OzBEQUd0RCw4REFBQ3FFO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ25GLHFLQUFHQTt3REFBQ21GLFdBQVU7Ozs7OztrRUFDZiw4REFBQ2E7d0RBQUtiLFdBQVU7a0VBQ2JsQixjQUNHLEdBQXVCQSxPQUFwQkEsWUFBWXhCLElBQUksRUFBQyxLQUF3QndCLE9BQXJCQSxZQUFZdkIsS0FBSyxFQUFDLEtBQW9CLE9BQWpCdUIsWUFBWXRCLElBQUksSUFDNUQ5QixFQUFFOzs7Ozs7Ozs7Ozs7MERBSVYsOERBQUNxRTtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNyRixxS0FBUUE7d0RBQUNxRixXQUFVOzs7Ozs7a0VBQ3BCLDhEQUFDYTt3REFBS2IsV0FBVTtrRUFDYnJFLFNBQVNLLFNBQVMsSUFBSUwsU0FBU00sT0FBTyxHQUNuQyxHQUEyQk4sT0FBeEJBLFNBQVNLLFNBQVMsRUFBQyxPQUFzQixPQUFqQkwsU0FBU00sT0FBTyxJQUMzQ1AsRUFBRSx1QkFBdUIsUUFBUUEsRUFBRTs7Ozs7Ozs7Ozs7OzRDQUkxQzJELG1CQUFtQixtQkFDbEIsOERBQUNVO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ2E7b0VBQUtiLFdBQVU7O3dFQUFpQ3RFLEVBQUU7d0VBQXFCOzs7Ozs7OzhFQUN4RSw4REFBQ21GO29FQUFLYixXQUFVOzt3RUFBYzt3RUFBRWxCLHdCQUFBQSxrQ0FBQUEsWUFBYXJCLElBQUk7Ozs7Ozs7Ozs7Ozs7c0VBRW5ELDhEQUFDc0M7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDYTtvRUFBS2IsV0FBVTs7d0VBQWlDdEUsRUFBRTt3RUFBb0I7Ozs7Ozs7OEVBQ3ZFLDhEQUFDbUY7b0VBQUtiLFdBQVU7O3dFQUNiTixLQUFLQyxJQUFJLENBQ1IsQ0FBQyxJQUFJSixLQUFLNUQsU0FBU00sT0FBTyxFQUFFMkQsT0FBTyxLQUFLLElBQUlMLEtBQUs1RCxTQUFTSyxTQUFTLEVBQUU0RCxPQUFPLEVBQUMsSUFDMUUsUUFBTyxLQUFLLEtBQUssRUFBQzt3RUFDcEI7d0VBQ0ZsRSxFQUFFOzs7Ozs7Ozs7Ozs7O3NFQUdQLDhEQUFDcUU7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDYTtvRUFBS2IsV0FBVTs7d0VBQWlCdEUsRUFBRTt3RUFBdUI7Ozs7Ozs7OEVBQzFELDhEQUFDbUY7b0VBQUtiLFdBQVU7O3dFQUFrQzt3RUFBRVg7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFZNUU7R0FsZHdCNUQ7O1FBQ1JoQixzRUFBV0E7OztLQURIZ0IiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hcHBsZS9EZXNrdG9wL09SX0VMIFJFTlRBTC9hcHAvZGFzaGJvYXJkL2Jvb2svcGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHR5cGUgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2FyZFwiXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvaW5wdXRcIlxuaW1wb3J0IHsgTGFiZWwgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2xhYmVsXCJcbmltcG9ydCB7IFRleHRhcmVhIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90ZXh0YXJlYVwiXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYmFkZ2VcIlxuaW1wb3J0IHsgRGFzaGJvYXJkSGVhZGVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy9kYXNoYm9hcmQtaGVhZGVyXCJcbmltcG9ydCB7IHVzZUxhbmd1YWdlIH0gZnJvbSBcIkAvY29tcG9uZW50cy9sYW5ndWFnZS1wcm92aWRlclwiXG5pbXBvcnQgeyBCb29rT3BlbiwgQ2FsZW5kYXIsIFVzZXIsIENhciwgU2VhcmNoLCBDaGVjaywgRmlsdGVyLCBGdWVsLCBVc2VycywgQ3Jvd24sIFRydWNrLCBNaW5pbWl6ZTIsIFphcCwgU3RhciB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIlxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBCb29rUGFnZSgpIHtcbiAgY29uc3QgeyB0IH0gPSB1c2VMYW5ndWFnZSgpXG4gIGNvbnN0IFtmb3JtRGF0YSwgc2V0Rm9ybURhdGFdID0gdXNlU3RhdGUoe1xuICAgIGNsaWVudE5hbWU6IFwiXCIsXG4gICAgY2xpZW50UGhvbmU6IFwiXCIsXG4gICAgY2FySWQ6IFwiXCIsXG4gICAgc3RhcnREYXRlOiBcIlwiLFxuICAgIGVuZERhdGU6IFwiXCIsXG4gICAgbm90ZXM6IFwiXCIsXG4gIH0pXG4gIGNvbnN0IFtjYXJTZWFyY2gsIHNldENhclNlYXJjaF0gPSB1c2VTdGF0ZShcIlwiKVxuICBjb25zdCBbc2hvd0NhckRyb3Bkb3duLCBzZXRTaG93Q2FyRHJvcGRvd25dID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtzZWxlY3RlZEJyYW5kLCBzZXRTZWxlY3RlZEJyYW5kXSA9IHVzZVN0YXRlKFwiYWxsXCIpXG4gIGNvbnN0IFtzZWxlY3RlZFR5cGUsIHNldFNlbGVjdGVkVHlwZV0gPSB1c2VTdGF0ZShcImFsbFwiKVxuXG4gIGNvbnN0IGNhckJyYW5kcyA9IHtcbiAgICBCTVc6IFwiaHR0cHM6Ly9sb2dvcy13b3JsZC5uZXQvd3AtY29udGVudC91cGxvYWRzLzIwMjAvMDMvQk1XLUxvZ28ucG5nXCIsXG4gICAgTWVyY2VkZXM6IFwiaHR0cHM6Ly9sb2dvcy13b3JsZC5uZXQvd3AtY29udGVudC91cGxvYWRzLzIwMjAvMDQvTWVyY2VkZXMtTG9nby5wbmdcIixcbiAgICBBdWRpOiBcImh0dHBzOi8vbG9nb3Mtd29ybGQubmV0L3dwLWNvbnRlbnQvdXBsb2Fkcy8yMDIwLzA0L0F1ZGktTG9nby5wbmdcIixcbiAgICBUb3lvdGE6IFwiaHR0cHM6Ly9sb2dvcy13b3JsZC5uZXQvd3AtY29udGVudC91cGxvYWRzLzIwMjAvMDMvVG95b3RhLUxvZ28ucG5nXCIsXG4gICAgVm9sa3N3YWdlbjogXCJodHRwczovL2xvZ29zLXdvcmxkLm5ldC93cC1jb250ZW50L3VwbG9hZHMvMjAyMC8wNC9Wb2xrc3dhZ2VuLUxvZ28ucG5nXCIsXG4gICAgRm9yZDogXCJodHRwczovL2xvZ29zLXdvcmxkLm5ldC93cC1jb250ZW50L3VwbG9hZHMvMjAyMC8wNC9Gb3JkLUxvZ28ucG5nXCIsXG4gICAgVGVzbGE6IFwiaHR0cHM6Ly9sb2dvcy13b3JsZC5uZXQvd3AtY29udGVudC91cGxvYWRzLzIwMjAvMDQvVGVzbGEtTG9nby5wbmdcIixcbiAgICBQb3JzY2hlOiBcImh0dHBzOi8vbG9nb3Mtd29ybGQubmV0L3dwLWNvbnRlbnQvdXBsb2Fkcy8yMDIwLzA0L1BvcnNjaGUtTG9nby5wbmdcIixcbiAgfVxuXG4gIGNvbnN0IGF2YWlsYWJsZUNhcnMgPSBbXG4gICAge1xuICAgICAgaWQ6IFwiMVwiLFxuICAgICAgbWFrZTogXCJCTVdcIixcbiAgICAgIG1vZGVsOiBcIlg1XCIsXG4gICAgICB5ZWFyOiAyMDIzLFxuICAgICAgcmF0ZTogMTIwLFxuICAgICAgYXZhaWxhYmxlOiB0cnVlLFxuICAgICAgdHlwZTogXCJsdXh1cnlcIixcbiAgICAgIGZ1ZWxUeXBlOiBcIlBldHJvbFwiLFxuICAgICAgc2VhdHM6IDUsXG4gICAgICB0cmFuc21pc3Npb246IFwiQXV0b1wiLFxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6IFwiMlwiLFxuICAgICAgbWFrZTogXCJNZXJjZWRlc1wiLFxuICAgICAgbW9kZWw6IFwiQy1DbGFzc1wiLFxuICAgICAgeWVhcjogMjAyMixcbiAgICAgIHJhdGU6IDEwMCxcbiAgICAgIGF2YWlsYWJsZTogdHJ1ZSxcbiAgICAgIHR5cGU6IFwibHV4dXJ5XCIsXG4gICAgICBmdWVsVHlwZTogXCJEaWVzZWxcIixcbiAgICAgIHNlYXRzOiA1LFxuICAgICAgdHJhbnNtaXNzaW9uOiBcIkF1dG9cIixcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiBcIjNcIixcbiAgICAgIG1ha2U6IFwiQXVkaVwiLFxuICAgICAgbW9kZWw6IFwiQTRcIixcbiAgICAgIHllYXI6IDIwMjMsXG4gICAgICByYXRlOiAxMTAsXG4gICAgICBhdmFpbGFibGU6IGZhbHNlLFxuICAgICAgdHlwZTogXCJzZWRhblwiLFxuICAgICAgZnVlbFR5cGU6IFwiUGV0cm9sXCIsXG4gICAgICBzZWF0czogNSxcbiAgICAgIHRyYW5zbWlzc2lvbjogXCJNYW51YWxcIixcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiBcIjRcIixcbiAgICAgIG1ha2U6IFwiVG95b3RhXCIsXG4gICAgICBtb2RlbDogXCJDYW1yeVwiLFxuICAgICAgeWVhcjogMjAyMixcbiAgICAgIHJhdGU6IDgwLFxuICAgICAgYXZhaWxhYmxlOiB0cnVlLFxuICAgICAgdHlwZTogXCJzZWRhblwiLFxuICAgICAgZnVlbFR5cGU6IFwiSHlicmlkXCIsXG4gICAgICBzZWF0czogNSxcbiAgICAgIHRyYW5zbWlzc2lvbjogXCJBdXRvXCIsXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogXCI1XCIsXG4gICAgICBtYWtlOiBcIkJNV1wiLFxuICAgICAgbW9kZWw6IFwiMyBTZXJpZXNcIixcbiAgICAgIHllYXI6IDIwMjMsXG4gICAgICByYXRlOiA5NSxcbiAgICAgIGF2YWlsYWJsZTogdHJ1ZSxcbiAgICAgIHR5cGU6IFwic2VkYW5cIixcbiAgICAgIGZ1ZWxUeXBlOiBcIlBldHJvbFwiLFxuICAgICAgc2VhdHM6IDUsXG4gICAgICB0cmFuc21pc3Npb246IFwiQXV0b1wiLFxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6IFwiNlwiLFxuICAgICAgbWFrZTogXCJUZXNsYVwiLFxuICAgICAgbW9kZWw6IFwiTW9kZWwgM1wiLFxuICAgICAgeWVhcjogMjAyMyxcbiAgICAgIHJhdGU6IDE0MCxcbiAgICAgIGF2YWlsYWJsZTogdHJ1ZSxcbiAgICAgIHR5cGU6IFwiZWxlY3RyaWNcIixcbiAgICAgIGZ1ZWxUeXBlOiBcIkVsZWN0cmljXCIsXG4gICAgICBzZWF0czogNSxcbiAgICAgIHRyYW5zbWlzc2lvbjogXCJBdXRvXCIsXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogXCI3XCIsXG4gICAgICBtYWtlOiBcIlZvbGtzd2FnZW5cIixcbiAgICAgIG1vZGVsOiBcIkdvbGZcIixcbiAgICAgIHllYXI6IDIwMjMsXG4gICAgICByYXRlOiA3MCxcbiAgICAgIGF2YWlsYWJsZTogdHJ1ZSxcbiAgICAgIHR5cGU6IFwiY29tcGFjdFwiLFxuICAgICAgZnVlbFR5cGU6IFwiUGV0cm9sXCIsXG4gICAgICBzZWF0czogNSxcbiAgICAgIHRyYW5zbWlzc2lvbjogXCJNYW51YWxcIixcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiBcIjhcIixcbiAgICAgIG1ha2U6IFwiRm9yZFwiLFxuICAgICAgbW9kZWw6IFwiRXhwbG9yZXJcIixcbiAgICAgIHllYXI6IDIwMjIsXG4gICAgICByYXRlOiA5NSxcbiAgICAgIGF2YWlsYWJsZTogdHJ1ZSxcbiAgICAgIHR5cGU6IFwic3V2XCIsXG4gICAgICBmdWVsVHlwZTogXCJQZXRyb2xcIixcbiAgICAgIHNlYXRzOiA3LFxuICAgICAgdHJhbnNtaXNzaW9uOiBcIkF1dG9cIixcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiBcIjlcIixcbiAgICAgIG1ha2U6IFwiUG9yc2NoZVwiLFxuICAgICAgbW9kZWw6IFwiQ2F5ZW5uZVwiLFxuICAgICAgeWVhcjogMjAyMyxcbiAgICAgIHJhdGU6IDIwMCxcbiAgICAgIGF2YWlsYWJsZTogdHJ1ZSxcbiAgICAgIHR5cGU6IFwibHV4dXJ5XCIsXG4gICAgICBmdWVsVHlwZTogXCJQZXRyb2xcIixcbiAgICAgIHNlYXRzOiA1LFxuICAgICAgdHJhbnNtaXNzaW9uOiBcIkF1dG9cIixcbiAgICB9LFxuICBdXG5cbiAgY29uc3QgY2FyVHlwZXMgPSBbXG4gICAgeyBpZDogXCJhbGxcIiwgbmFtZTogdChcImNvbW1vbi5hbGxcIiksIGljb246IEZpbHRlciB9LFxuICAgIHsgaWQ6IFwibHV4dXJ5XCIsIG5hbWU6IHQoXCJjYXJUeXBlLmx1eHVyeVwiKSwgaWNvbjogQ3Jvd24gfSxcbiAgICB7IGlkOiBcInN1dlwiLCBuYW1lOiB0KFwiY2FyVHlwZS5zdXZcIiksIGljb246IFRydWNrIH0sXG4gICAgeyBpZDogXCJzZWRhblwiLCBuYW1lOiB0KFwiY2FyVHlwZS5zZWRhblwiKSwgaWNvbjogQ2FyIH0sXG4gICAgeyBpZDogXCJjb21wYWN0XCIsIG5hbWU6IHQoXCJjYXJUeXBlLmNvbXBhY3RcIiksIGljb246IE1pbmltaXplMiB9LFxuICAgIHsgaWQ6IFwiZWxlY3RyaWNcIiwgbmFtZTogdChcImNhclR5cGUuZWxlY3RyaWNcIiksIGljb246IFphcCB9LFxuICBdXG5cbiAgY29uc3QgYnJhbmRzID0gW1wiYWxsXCIsIC4uLk9iamVjdC5rZXlzKGNhckJyYW5kcyldXG5cbiAgY29uc3QgZmlsdGVyZWRDYXJzID0gYXZhaWxhYmxlQ2Fycy5maWx0ZXIoKGNhcikgPT4ge1xuICAgIGNvbnN0IG1hdGNoZXNTZWFyY2ggPVxuICAgICAgY2FyLm1ha2UudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhjYXJTZWFyY2gudG9Mb3dlckNhc2UoKSkgfHxcbiAgICAgIGNhci5tb2RlbC50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGNhclNlYXJjaC50b0xvd2VyQ2FzZSgpKSB8fFxuICAgICAgYCR7Y2FyLm1ha2V9ICR7Y2FyLm1vZGVsfWAudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhjYXJTZWFyY2gudG9Mb3dlckNhc2UoKSlcblxuICAgIGNvbnN0IG1hdGNoZXNCcmFuZCA9IHNlbGVjdGVkQnJhbmQgPT09IFwiYWxsXCIgfHwgY2FyLm1ha2UgPT09IHNlbGVjdGVkQnJhbmRcbiAgICBjb25zdCBtYXRjaGVzVHlwZSA9IHNlbGVjdGVkVHlwZSA9PT0gXCJhbGxcIiB8fCBjYXIudHlwZSA9PT0gc2VsZWN0ZWRUeXBlXG4gICAgY29uc3QgaXNBdmFpbGFibGUgPSBjYXIuYXZhaWxhYmxlXG5cbiAgICByZXR1cm4gbWF0Y2hlc1NlYXJjaCAmJiBtYXRjaGVzQnJhbmQgJiYgbWF0Y2hlc1R5cGUgJiYgaXNBdmFpbGFibGVcbiAgfSlcblxuICBjb25zdCBzZWxlY3RlZENhciA9IGF2YWlsYWJsZUNhcnMuZmluZCgoY2FyKSA9PiBjYXIuaWQgPT09IGZvcm1EYXRhLmNhcklkKVxuXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IChlOiBSZWFjdC5Gb3JtRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KClcbiAgICBjb25zb2xlLmxvZyhcIkJvb2tpbmcgZGF0YTpcIiwgZm9ybURhdGEpXG4gICAgLy8gSGFuZGxlIGJvb2tpbmcgc3VibWlzc2lvblxuICB9XG5cbiAgY29uc3QgY2FsY3VsYXRlVG90YWwgPSAoKSA9PiB7XG4gICAgaWYgKGZvcm1EYXRhLnN0YXJ0RGF0ZSAmJiBmb3JtRGF0YS5lbmREYXRlICYmIGZvcm1EYXRhLmNhcklkKSB7XG4gICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKGZvcm1EYXRhLnN0YXJ0RGF0ZSlcbiAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKGZvcm1EYXRhLmVuZERhdGUpXG4gICAgICBjb25zdCBkYXlzID0gTWF0aC5jZWlsKChlbmQuZ2V0VGltZSgpIC0gc3RhcnQuZ2V0VGltZSgpKSAvICgxMDAwICogNjAgKiA2MCAqIDI0KSlcbiAgICAgIGNvbnN0IGNhciA9IGF2YWlsYWJsZUNhcnMuZmluZCgoYykgPT4gYy5pZCA9PT0gZm9ybURhdGEuY2FySWQpXG4gICAgICByZXR1cm4gZGF5cyA+IDAgJiYgY2FyID8gZGF5cyAqIGNhci5yYXRlIDogMFxuICAgIH1cbiAgICByZXR1cm4gMFxuICB9XG5cbiAgY29uc3Qgc2VsZWN0Q2FyID0gKGNhcjogKHR5cGVvZiBhdmFpbGFibGVDYXJzKVswXSkgPT4ge1xuICAgIHNldEZvcm1EYXRhKHsgLi4uZm9ybURhdGEsIGNhcklkOiBjYXIuaWQgfSlcbiAgICBzZXRDYXJTZWFyY2goYCR7Y2FyLm1ha2V9ICR7Y2FyLm1vZGVsfSAke2Nhci55ZWFyfWApXG4gICAgc2V0U2hvd0NhckRyb3Bkb3duKGZhbHNlKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWluLWgtc2NyZWVuXCI+XG4gICAgICA8RGFzaGJvYXJkSGVhZGVyIHRpdGxlPXt0KFwibmF2LmJvb2tcIil9IHN1YnRpdGxlPXt0KFwiYm9va2luZy50aXRsZVwiKX0gc2hvd0FjdGlvbnM9e2ZhbHNlfSAvPlxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBwLTYgc3BhY2UteS02IGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmFja2dyb3VuZCB0by1tdXRlZC8yMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTMgZ2FwLTZcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTJcIj5cbiAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImNhcmQtaG92ZXIgYm9yZGVyLTAgc2hhZG93LWxnXCI+XG4gICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxCb29rT3BlbiBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICAgICAgICAgIHt0KFwiYm9va2luZy50aXRsZVwiKX1cbiAgICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0gY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImNsaWVudE5hbWVcIj57dChcImJvb2tpbmcuY2xpZW50TmFtZVwiKX0gKjwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cImNsaWVudE5hbWVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmNsaWVudE5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHsgLi4uZm9ybURhdGEsIGNsaWVudE5hbWU6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e3QoXCJib29raW5nLmNsaWVudE5hbWVcIil9XG4gICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC0xMlwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJjbGllbnRQaG9uZVwiPnt0KFwiYm9va2luZy5jbGllbnRQaG9uZVwiKX0gKjwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cImNsaWVudFBob25lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jbGllbnRQaG9uZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoeyAuLi5mb3JtRGF0YSwgY2xpZW50UGhvbmU6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e3QoXCJib29raW5nLmNsaWVudFBob25lXCIpfVxuICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtMTJcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBTbWFydCBDYXIgU2VsZWN0aW9uICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPExhYmVsPnt0KFwiYm9va2luZy5zZWxlY3RDYXJcIil9ICo8L0xhYmVsPlxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiBTZWFyY2ggYW5kIEZpbHRlcnMgKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFNlYXJjaCBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBoLTQgdy00IHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NhclNlYXJjaH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0Q2FyU2VhcmNoKGUudGFyZ2V0LnZhbHVlKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFNob3dDYXJEcm9wZG93bih0cnVlKVxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkZvY3VzPXsoKSA9PiBzZXRTaG93Q2FyRHJvcGRvd24odHJ1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXt0KFwiYm9va2luZy5zZWFyY2hDYXJzXCIpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwbC0xMCBoLTEyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICB7LyogRmlsdGVyIEJ1dHRvbnMgKi99XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8RmlsdGVyIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj57dChcImJvb2tpbmcuZmlsdGVyQnlUeXBlXCIpfTo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjYXJUeXBlcy5tYXAoKHR5cGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBJY29uQ29tcG9uZW50ID0gdHlwZS5pY29uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXt0eXBlLmlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD17c2VsZWN0ZWRUeXBlID09PSB0eXBlLmlkID8gXCJkZWZhdWx0XCIgOiBcIm91dGxpbmVcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWRUeXBlKHR5cGUuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTggcHgtMyB0ZXh0LXhzIGZsZXggaXRlbXMtY2VudGVyIGdhcC0xLjVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SWNvbkNvbXBvbmVudCBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3R5cGUubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFN0YXIgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPnt0KFwiYm9va2luZy5maWx0ZXJCeUJyYW5kXCIpfTo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2JyYW5kcy5tYXAoKGJyYW5kKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXticmFuZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD17c2VsZWN0ZWRCcmFuZCA9PT0gYnJhbmQgPyBcImRlZmF1bHRcIiA6IFwib3V0bGluZVwifVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlbGVjdGVkQnJhbmQoYnJhbmQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC04IHB4LTMgdGV4dC14cyBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2JyYW5kICE9PSBcImFsbFwiICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNyYz17Y2FyQnJhbmRzW2JyYW5kIGFzIGtleW9mIHR5cGVvZiBjYXJCcmFuZHNdIHx8IFwiL3BsYWNlaG9sZGVyLnN2Z1wifVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsdD17YnJhbmR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy00IGgtNCBvYmplY3QtY29udGFpblwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2JyYW5kID09PSBcImFsbFwiID8gdChcImNvbW1vbi5hbGxcIikgOiBicmFuZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIEF2YWlsYWJsZSBDYXJzIERpc3BsYXkgKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyIHJvdW5kZWQtbGcgcC00IGJnLW11dGVkLzIwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIG1iLTMgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDYXIgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICB7dChcImJvb2tpbmcuYXZhaWxhYmxlQ2Fyc1wiKX0gKHtmaWx0ZXJlZENhcnMubGVuZ3RofSlcbiAgICAgICAgICAgICAgICAgICAgICA8L2g0PlxuXG4gICAgICAgICAgICAgICAgICAgICAge2ZpbHRlcmVkQ2Fycy5sZW5ndGggPiAwID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC0zIG1heC1oLTYwIG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7ZmlsdGVyZWRDYXJzLm1hcCgoY2FyKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtjYXIuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTMgYm9yZGVyIHJvdW5kZWQtbGcgY3Vyc29yLXBvaW50ZXIgdHJhbnNpdGlvbi1hbGwgaG92ZXI6c2hhZG93LW1kICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvcm1EYXRhLmNhcklkID09PSBjYXIuaWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiYm9yZGVyLXByaW1hcnkgYmctcHJpbWFyeS8xMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcImJvcmRlci1ib3JkZXIgaG92ZXI6Ym9yZGVyLXByaW1hcnkvNTBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZWxlY3RDYXIoY2FyKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzcmM9e2NhckJyYW5kc1tjYXIubWFrZSBhcyBrZXlvZiB0eXBlb2YgY2FyQnJhbmRzXSB8fCBcIi9wbGFjZWhvbGRlci5zdmdcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsdD17Y2FyLm1ha2V9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTYgaC02IG9iamVjdC1jb250YWluXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjYXIubWFrZX0ge2Nhci5tb2RlbH0ge2Nhci55ZWFyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJzZWNvbmRhcnlcIiBjbGFzc05hbWU9XCJ0ZXh0LXhzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0KGBjYXJUeXBlLiR7Y2FyLnR5cGV9YCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1EYXRhLmNhcklkID09PSBjYXIuaWQgJiYgPENoZWNrIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1wcmltYXJ5XCIgLz59XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gdGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxGdWVsIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Nhci5mdWVsVHlwZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VXNlcnMgY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y2FyLnNlYXRzfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1ib2xkIHRleHQtcHJpbWFyeVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIOKCrHtjYXIucmF0ZX0ve3QoXCJ0aW1lLmRheXNcIil9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciB0ZXh0LW11dGVkLWZvcmVncm91bmQgcHktNFwiPnt0KFwiYm9va2luZy5ub0F2YWlsYWJsZUNhcnNcIil9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwic3RhcnREYXRlXCI+e3QoXCJib29raW5nLnN0YXJ0RGF0ZVwiKX0gKjwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cInN0YXJ0RGF0ZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuc3RhcnREYXRlfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7IC4uLmZvcm1EYXRhLCBzdGFydERhdGU6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtMTJcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiZW5kRGF0ZVwiPnt0KFwiYm9va2luZy5lbmREYXRlXCIpfSAqPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiZW5kRGF0ZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZW5kRGF0ZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoeyAuLi5mb3JtRGF0YSwgZW5kRGF0ZTogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC0xMlwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJub3Rlc1wiPnt0KFwiYm9va2luZy5ub3Rlc1wiKX08L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgICAgICBpZD1cIm5vdGVzXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEubm90ZXN9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7IC4uLmZvcm1EYXRhLCBub3RlczogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e3QoXCJib29raW5nLm5vdGVzXCIpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1pbi1oLVsxMDBweF1cIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxCdXR0b24gdHlwZT1cInN1Ym1pdFwiIGNsYXNzTmFtZT1cInctZnVsbCBoLTEyIG1vZGVybi1idXR0b24gZ3JhZGllbnQtYmcgdGV4dC1sZyBmb250LXNlbWlib2xkXCI+XG4gICAgICAgICAgICAgICAgICAgIHt0KFwiYm9va2luZy5jcmVhdGVcIil9XG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Zvcm0+XG4gICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiY2FyZC1ob3ZlciBib3JkZXItMCBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgPENhcmRUaXRsZT57dChcImJvb2tpbmcuc3VtbWFyeVwiKX08L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPntmb3JtRGF0YS5jbGllbnROYW1lIHx8IHQoXCJib29raW5nLmNsaWVudE5hbWVcIil9PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPENhciBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZENhclxuICAgICAgICAgICAgICAgICAgICAgID8gYCR7c2VsZWN0ZWRDYXIubWFrZX0gJHtzZWxlY3RlZENhci5tb2RlbH0gJHtzZWxlY3RlZENhci55ZWFyfWBcbiAgICAgICAgICAgICAgICAgICAgICA6IHQoXCJib29raW5nLnNlbGVjdENhclwiKX1cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxDYWxlbmRhciBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtRGF0YS5zdGFydERhdGUgJiYgZm9ybURhdGEuZW5kRGF0ZVxuICAgICAgICAgICAgICAgICAgICAgID8gYCR7Zm9ybURhdGEuc3RhcnREYXRlfSAtICR7Zm9ybURhdGEuZW5kRGF0ZX1gXG4gICAgICAgICAgICAgICAgICAgICAgOiB0KFwiYm9va2luZy5zdGFydERhdGVcIikgKyBcIiAtIFwiICsgdChcImJvb2tpbmcuZW5kRGF0ZVwiKX1cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHtjYWxjdWxhdGVUb3RhbCgpID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci10IHB0LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj57dChcImJvb2tpbmcuZGFpbHlSYXRlXCIpfTo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPuKCrHtzZWxlY3RlZENhcj8ucmF0ZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+e3QoXCJib29raW5nLmR1cmF0aW9uXCIpfTo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7TWF0aC5jZWlsKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIChuZXcgRGF0ZShmb3JtRGF0YS5lbmREYXRlKS5nZXRUaW1lKCkgLSBuZXcgRGF0ZShmb3JtRGF0YS5zdGFydERhdGUpLmdldFRpbWUoKSkgL1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKDEwMDAgKiA2MCAqIDYwICogMjQpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICApfXtcIiBcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3QoXCJ0aW1lLmRheXNcIil9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgcHQtMiBib3JkZXItdFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZFwiPnt0KFwiYm9va2luZy50b3RhbEFtb3VudFwiKX06PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtcHJpbWFyeVwiPuKCrHtjYWxjdWxhdGVUb3RhbCgpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkJ1dHRvbiIsIklucHV0IiwiTGFiZWwiLCJUZXh0YXJlYSIsIkJhZGdlIiwiRGFzaGJvYXJkSGVhZGVyIiwidXNlTGFuZ3VhZ2UiLCJCb29rT3BlbiIsIkNhbGVuZGFyIiwiVXNlciIsIkNhciIsIlNlYXJjaCIsIkNoZWNrIiwiRmlsdGVyIiwiRnVlbCIsIlVzZXJzIiwiQ3Jvd24iLCJUcnVjayIsIk1pbmltaXplMiIsIlphcCIsIlN0YXIiLCJ1c2VTdGF0ZSIsIkJvb2tQYWdlIiwidCIsImZvcm1EYXRhIiwic2V0Rm9ybURhdGEiLCJjbGllbnROYW1lIiwiY2xpZW50UGhvbmUiLCJjYXJJZCIsInN0YXJ0RGF0ZSIsImVuZERhdGUiLCJub3RlcyIsImNhclNlYXJjaCIsInNldENhclNlYXJjaCIsInNob3dDYXJEcm9wZG93biIsInNldFNob3dDYXJEcm9wZG93biIsInNlbGVjdGVkQnJhbmQiLCJzZXRTZWxlY3RlZEJyYW5kIiwic2VsZWN0ZWRUeXBlIiwic2V0U2VsZWN0ZWRUeXBlIiwiY2FyQnJhbmRzIiwiQk1XIiwiTWVyY2VkZXMiLCJBdWRpIiwiVG95b3RhIiwiVm9sa3N3YWdlbiIsIkZvcmQiLCJUZXNsYSIsIlBvcnNjaGUiLCJhdmFpbGFibGVDYXJzIiwiaWQiLCJtYWtlIiwibW9kZWwiLCJ5ZWFyIiwicmF0ZSIsImF2YWlsYWJsZSIsInR5cGUiLCJmdWVsVHlwZSIsInNlYXRzIiwidHJhbnNtaXNzaW9uIiwiY2FyVHlwZXMiLCJuYW1lIiwiaWNvbiIsImJyYW5kcyIsIk9iamVjdCIsImtleXMiLCJmaWx0ZXJlZENhcnMiLCJmaWx0ZXIiLCJjYXIiLCJtYXRjaGVzU2VhcmNoIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsIm1hdGNoZXNCcmFuZCIsIm1hdGNoZXNUeXBlIiwiaXNBdmFpbGFibGUiLCJzZWxlY3RlZENhciIsImZpbmQiLCJoYW5kbGVTdWJtaXQiLCJlIiwicHJldmVudERlZmF1bHQiLCJjb25zb2xlIiwibG9nIiwiY2FsY3VsYXRlVG90YWwiLCJzdGFydCIsIkRhdGUiLCJlbmQiLCJkYXlzIiwiTWF0aCIsImNlaWwiLCJnZXRUaW1lIiwiYyIsInNlbGVjdENhciIsImRpdiIsImNsYXNzTmFtZSIsInRpdGxlIiwic3VidGl0bGUiLCJzaG93QWN0aW9ucyIsImZvcm0iLCJvblN1Ym1pdCIsImh0bWxGb3IiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwidGFyZ2V0IiwicGxhY2Vob2xkZXIiLCJyZXF1aXJlZCIsIm9uRm9jdXMiLCJzcGFuIiwibWFwIiwiSWNvbkNvbXBvbmVudCIsInZhcmlhbnQiLCJzaXplIiwib25DbGljayIsImJyYW5kIiwiaW1nIiwic3JjIiwiYWx0IiwiaDQiLCJsZW5ndGgiLCJwIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/book/page.tsx\n"));

/***/ })

});