"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/book/page",{

/***/ "(app-pages-browser)/./app/dashboard/book/page.tsx":
/*!*************************************!*\
  !*** ./app/dashboard/book/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BookPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_dashboard_header__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard-header */ \"(app-pages-browser)/./components/dashboard-header.tsx\");\n/* harmony import */ var _components_language_provider__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/language-provider */ \"(app-pages-browser)/./components/language-provider.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Filter_Fuel_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Filter,Fuel,Search,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Filter_Fuel_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Filter,Fuel,Search,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Filter_Fuel_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Filter,Fuel,Search,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Filter_Fuel_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Filter,Fuel,Search,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Filter_Fuel_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Filter,Fuel,Search,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Filter_Fuel_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Filter,Fuel,Search,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/fuel.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Filter_Fuel_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Filter,Fuel,Search,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Filter_Fuel_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Filter,Fuel,Search,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_Check_Filter_Fuel_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,Check,Filter,Fuel,Search,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction BookPage() {\n    _s();\n    const { t } = (0,_components_language_provider__WEBPACK_IMPORTED_MODULE_8__.useLanguage)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)({\n        clientName: \"\",\n        clientPhone: \"\",\n        carId: \"\",\n        startDate: \"\",\n        endDate: \"\",\n        notes: \"\"\n    });\n    const [carSearch, setCarSearch] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(\"\");\n    const [showCarDropdown, setShowCarDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [selectedBrand, setSelectedBrand] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(\"all\");\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(\"all\");\n    const carBrands = {\n        BMW: \"https://logos-world.net/wp-content/uploads/2020/03/BMW-Logo.png\",\n        Mercedes: \"https://logos-world.net/wp-content/uploads/2020/04/Mercedes-Logo.png\",\n        Audi: \"https://logos-world.net/wp-content/uploads/2020/04/Audi-Logo.png\",\n        Toyota: \"https://logos-world.net/wp-content/uploads/2020/03/Toyota-Logo.png\",\n        Volkswagen: \"https://logos-world.net/wp-content/uploads/2020/04/Volkswagen-Logo.png\",\n        Ford: \"https://logos-world.net/wp-content/uploads/2020/04/Ford-Logo.png\",\n        Tesla: \"https://logos-world.net/wp-content/uploads/2020/04/Tesla-Logo.png\",\n        Porsche: \"https://logos-world.net/wp-content/uploads/2020/04/Porsche-Logo.png\"\n    };\n    const availableCars = [\n        {\n            id: \"1\",\n            make: \"BMW\",\n            model: \"X5\",\n            year: 2023,\n            rate: 120,\n            available: true,\n            type: \"luxury\",\n            fuelType: \"Petrol\",\n            seats: 5,\n            transmission: \"Auto\"\n        },\n        {\n            id: \"2\",\n            make: \"Mercedes\",\n            model: \"C-Class\",\n            year: 2022,\n            rate: 100,\n            available: true,\n            type: \"luxury\",\n            fuelType: \"Diesel\",\n            seats: 5,\n            transmission: \"Auto\"\n        },\n        {\n            id: \"3\",\n            make: \"Audi\",\n            model: \"A4\",\n            year: 2023,\n            rate: 110,\n            available: false,\n            type: \"sedan\",\n            fuelType: \"Petrol\",\n            seats: 5,\n            transmission: \"Manual\"\n        },\n        {\n            id: \"4\",\n            make: \"Toyota\",\n            model: \"Camry\",\n            year: 2022,\n            rate: 80,\n            available: true,\n            type: \"sedan\",\n            fuelType: \"Hybrid\",\n            seats: 5,\n            transmission: \"Auto\"\n        },\n        {\n            id: \"5\",\n            make: \"BMW\",\n            model: \"3 Series\",\n            year: 2023,\n            rate: 95,\n            available: true,\n            type: \"sedan\",\n            fuelType: \"Petrol\",\n            seats: 5,\n            transmission: \"Auto\"\n        },\n        {\n            id: \"6\",\n            make: \"Tesla\",\n            model: \"Model 3\",\n            year: 2023,\n            rate: 140,\n            available: true,\n            type: \"electric\",\n            fuelType: \"Electric\",\n            seats: 5,\n            transmission: \"Auto\"\n        },\n        {\n            id: \"7\",\n            make: \"Volkswagen\",\n            model: \"Golf\",\n            year: 2023,\n            rate: 70,\n            available: true,\n            type: \"compact\",\n            fuelType: \"Petrol\",\n            seats: 5,\n            transmission: \"Manual\"\n        },\n        {\n            id: \"8\",\n            make: \"Ford\",\n            model: \"Explorer\",\n            year: 2022,\n            rate: 95,\n            available: true,\n            type: \"suv\",\n            fuelType: \"Petrol\",\n            seats: 7,\n            transmission: \"Auto\"\n        },\n        {\n            id: \"9\",\n            make: \"Porsche\",\n            model: \"Cayenne\",\n            year: 2023,\n            rate: 200,\n            available: true,\n            type: \"luxury\",\n            fuelType: \"Petrol\",\n            seats: 5,\n            transmission: \"Auto\"\n        }\n    ];\n    const carTypes = [\n        {\n            id: \"all\",\n            name: t(\"common.all\")\n        },\n        {\n            id: \"luxury\",\n            name: t(\"carType.luxury\")\n        },\n        {\n            id: \"suv\",\n            name: t(\"carType.suv\")\n        },\n        {\n            id: \"sedan\",\n            name: t(\"carType.sedan\")\n        },\n        {\n            id: \"compact\",\n            name: t(\"carType.compact\")\n        },\n        {\n            id: \"electric\",\n            name: t(\"carType.electric\")\n        }\n    ];\n    const brands = [\n        \"all\",\n        ...Object.keys(carBrands)\n    ];\n    const filteredCars = availableCars.filter((car)=>{\n        const matchesSearch = car.make.toLowerCase().includes(carSearch.toLowerCase()) || car.model.toLowerCase().includes(carSearch.toLowerCase()) || \"\".concat(car.make, \" \").concat(car.model).toLowerCase().includes(carSearch.toLowerCase());\n        const matchesBrand = selectedBrand === \"all\" || car.make === selectedBrand;\n        const matchesType = selectedType === \"all\" || car.type === selectedType;\n        const isAvailable = car.available;\n        return matchesSearch && matchesBrand && matchesType && isAvailable;\n    });\n    const selectedCar = availableCars.find((car)=>car.id === formData.carId);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        console.log(\"Booking data:\", formData);\n    // Handle booking submission\n    };\n    const calculateTotal = ()=>{\n        if (formData.startDate && formData.endDate && formData.carId) {\n            const start = new Date(formData.startDate);\n            const end = new Date(formData.endDate);\n            const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));\n            const car = availableCars.find((c)=>c.id === formData.carId);\n            return days > 0 && car ? days * car.rate : 0;\n        }\n        return 0;\n    };\n    const selectCar = (car)=>{\n        setFormData({\n            ...formData,\n            carId: car.id\n        });\n        setCarSearch(\"\".concat(car.make, \" \").concat(car.model, \" \").concat(car.year));\n        setShowCarDropdown(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_header__WEBPACK_IMPORTED_MODULE_7__.DashboardHeader, {\n                title: t(\"nav.book\"),\n                subtitle: t(\"booking.title\"),\n                showActions: false\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-6 space-y-6 bg-gradient-to-br from-background to-muted/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                className: \"card-hover border-0 shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Filter_Fuel_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this),\n                                                t(\"booking.title\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"clientName\",\n                                                                    children: [\n                                                                        t(\"booking.clientName\"),\n                                                                        \" *\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                    id: \"clientName\",\n                                                                    value: formData.clientName,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            clientName: e.target.value\n                                                                        }),\n                                                                    placeholder: t(\"booking.clientName\"),\n                                                                    required: true,\n                                                                    className: \"h-12\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"clientPhone\",\n                                                                    children: [\n                                                                        t(\"booking.clientPhone\"),\n                                                                        \" *\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                    id: \"clientPhone\",\n                                                                    value: formData.clientPhone,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            clientPhone: e.target.value\n                                                                        }),\n                                                                    placeholder: t(\"booking.clientPhone\"),\n                                                                    required: true,\n                                                                    className: \"h-12\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            children: [\n                                                                t(\"booking.selectCar\"),\n                                                                \" *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Filter_Fuel_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                            lineNumber: 250,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                            value: carSearch,\n                                                                            onChange: (e)=>{\n                                                                                setCarSearch(e.target.value);\n                                                                                setShowCarDropdown(true);\n                                                                            },\n                                                                            onFocus: ()=>setShowCarDropdown(true),\n                                                                            placeholder: t(\"booking.searchCars\"),\n                                                                            className: \"pl-10 h-12\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                            lineNumber: 251,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Filter_Fuel_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-muted-foreground\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                lineNumber: 266,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-muted-foreground\",\n                                                                                children: [\n                                                                                    t(\"booking.filterByType\"),\n                                                                                    \":\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                lineNumber: 267,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            carTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    type: \"button\",\n                                                                                    variant: selectedType === type.id ? \"default\" : \"outline\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>setSelectedType(type.id),\n                                                                                    className: \"h-8 px-3 text-xs\",\n                                                                                    children: type.name\n                                                                                }, type.id, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                    lineNumber: 269,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                        lineNumber: 265,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: [\n                                                                                t(\"booking.filterByBrand\"),\n                                                                                \":\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                            lineNumber: 284,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        brands.map((brand)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                type: \"button\",\n                                                                                variant: selectedBrand === brand ? \"default\" : \"outline\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>setSelectedBrand(brand),\n                                                                                className: \"h-8 px-3 text-xs flex items-center gap-2\",\n                                                                                children: [\n                                                                                    brand !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                        src: carBrands[brand] || \"/placeholder.svg\",\n                                                                                        alt: brand,\n                                                                                        className: \"w-4 h-4 object-contain\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                        lineNumber: 295,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    brand === \"all\" ? t(\"common.all\") : brand\n                                                                                ]\n                                                                            }, brand, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                lineNumber: 286,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border rounded-lg p-4 bg-muted/20\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium mb-3 flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Filter_Fuel_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        t(\"booking.availableCars\"),\n                                                                        \" (\",\n                                                                        filteredCars.length,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                filteredCars.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-3 max-h-60 overflow-y-auto\",\n                                                                    children: filteredCars.map((car)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md \".concat(formData.carId === car.id ? \"border-primary bg-primary/10\" : \"border-border hover:border-primary/50\"),\n                                                                            onClick: ()=>selectCar(car),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-between mb-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                                    src: carBrands[car.make] || \"/placeholder.svg\",\n                                                                                                    alt: car.make,\n                                                                                                    className: \"w-6 h-6 object-contain\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                    lineNumber: 328,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"font-medium text-sm\",\n                                                                                                            children: [\n                                                                                                                car.make,\n                                                                                                                \" \",\n                                                                                                                car.model,\n                                                                                                                \" \",\n                                                                                                                car.year\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                            lineNumber: 334,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                                                            variant: \"secondary\",\n                                                                                                            className: \"text-xs\",\n                                                                                                            children: t(\"carType.\".concat(car.type))\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                            lineNumber: 337,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                    lineNumber: 333,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                            lineNumber: 327,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        formData.carId === car.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Filter_Fuel_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                            className: \"h-4 w-4 text-primary\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                            lineNumber: 342,\n                                                                                            columnNumber: 63\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                    lineNumber: 326,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center justify-between text-xs text-muted-foreground\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-3\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex items-center gap-1\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Filter_Fuel_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                                            className: \"h-3 w-3\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                            lineNumber: 348,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this),\n                                                                                                        car.fuelType\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                    lineNumber: 347,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex items-center gap-1\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Filter_Fuel_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                                            className: \"h-3 w-3\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                            lineNumber: 352,\n                                                                                                            columnNumber: 37\n                                                                                                        }, this),\n                                                                                                        car.seats\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                                    lineNumber: 351,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                            lineNumber: 346,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"font-bold text-primary\",\n                                                                                            children: [\n                                                                                                \"€\",\n                                                                                                car.rate,\n                                                                                                \"/\",\n                                                                                                t(\"time.days\")\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                            lineNumber: 356,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                                    lineNumber: 345,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, car.id, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                            lineNumber: 317,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 315,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-center text-muted-foreground py-4\",\n                                                                    children: t(\"booking.noAvailableCars\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"startDate\",\n                                                                    children: [\n                                                                        t(\"booking.startDate\"),\n                                                                        \" *\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                    id: \"startDate\",\n                                                                    type: \"date\",\n                                                                    value: formData.startDate,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            startDate: e.target.value\n                                                                        }),\n                                                                    required: true,\n                                                                    className: \"h-12\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"endDate\",\n                                                                    children: [\n                                                                        t(\"booking.endDate\"),\n                                                                        \" *\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                    id: \"endDate\",\n                                                                    type: \"date\",\n                                                                    value: formData.endDate,\n                                                                    onChange: (e)=>setFormData({\n                                                                            ...formData,\n                                                                            endDate: e.target.value\n                                                                        }),\n                                                                    required: true,\n                                                                    className: \"h-12\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"notes\",\n                                                            children: t(\"booking.notes\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                            id: \"notes\",\n                                                            value: formData.notes,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    notes: e.target.value\n                                                                }),\n                                                            placeholder: t(\"booking.notes\"),\n                                                            className: \"min-h-[100px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    type: \"submit\",\n                                                    className: \"w-full h-12 modern-button gradient-bg text-lg font-semibold\",\n                                                    children: t(\"booking.create\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                className: \"card-hover border-0 shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                            children: t(\"booking.summary\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Filter_Fuel_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: formData.clientName || t(\"booking.clientName\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Filter_Fuel_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: selectedCar ? \"\".concat(selectedCar.make, \" \").concat(selectedCar.model, \" \").concat(selectedCar.year) : t(\"booking.selectCar\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_Check_Filter_Fuel_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: formData.startDate && formData.endDate ? \"\".concat(formData.startDate, \" - \").concat(formData.endDate) : t(\"booking.startDate\") + \" - \" + t(\"booking.endDate\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this),\n                                            calculateTotal() > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: [\n                                                                        t(\"booking.dailyRate\"),\n                                                                        \":\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        \"€\",\n                                                                        selectedCar === null || selectedCar === void 0 ? void 0 : selectedCar.rate\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 447,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: [\n                                                                        t(\"booking.duration\"),\n                                                                        \":\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        Math.ceil((new Date(formData.endDate).getTime() - new Date(formData.startDate).getTime()) / (1000 * 60 * 60 * 24)),\n                                                                        \" \",\n                                                                        t(\"time.days\")\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center pt-2 border-t\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: [\n                                                                        t(\"booking.totalAmount\"),\n                                                                        \":\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 460,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold text-primary\",\n                                                                    children: [\n                                                                        \"€\",\n                                                                        calculateTotal()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/app/dashboard/book/page.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n}\n_s(BookPage, \"lDHKySXaZLtAgH/XLjGzkLz5nn8=\", false, function() {\n    return [\n        _components_language_provider__WEBPACK_IMPORTED_MODULE_8__.useLanguage\n    ];\n});\n_c = BookPage;\nvar _c;\n$RefreshReg$(_c, \"BookPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/book/page.tsx\n"));

/***/ })

});