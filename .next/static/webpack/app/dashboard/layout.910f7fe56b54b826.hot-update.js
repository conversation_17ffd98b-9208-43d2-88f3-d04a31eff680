"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./components/app-sidebar.tsx":
/*!************************************!*\
  !*** ./components/app-sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: () => (/* binding */ AppSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_ChevronRight_Clock_FileText_UserCheck_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,ChevronRight,Clock,FileText,UserCheck,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_ChevronRight_Clock_FileText_UserCheck_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,ChevronRight,Clock,FileText,UserCheck,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_ChevronRight_Clock_FileText_UserCheck_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,ChevronRight,Clock,FileText,UserCheck,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_ChevronRight_Clock_FileText_UserCheck_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,ChevronRight,Clock,FileText,UserCheck,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_ChevronRight_Clock_FileText_UserCheck_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,ChevronRight,Clock,FileText,UserCheck,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_ChevronRight_Clock_FileText_UserCheck_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,ChevronRight,Clock,FileText,UserCheck,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_ChevronRight_Clock_FileText_UserCheck_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,ChevronRight,Clock,FileText,UserCheck,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Car_ChevronRight_Clock_FileText_UserCheck_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Car,ChevronRight,Clock,FileText,UserCheck,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./components/ui/sidebar.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _components_language_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/language-provider */ \"(app-pages-browser)/./components/language-provider.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AppSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst menuItems = [\n    {\n        title: \"nav.calendar\",\n        url: \"/dashboard/calendar\",\n        icon: _barrel_optimize_names_BookOpen_Calendar_Car_ChevronRight_Clock_FileText_UserCheck_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        badge: \"3\",\n        description: \"View active orders\"\n    },\n    {\n        title: \"nav.book\",\n        url: \"/dashboard/book\",\n        icon: _barrel_optimize_names_BookOpen_Calendar_Car_ChevronRight_Clock_FileText_UserCheck_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        description: \"Create new booking\"\n    },\n    {\n        title: \"nav.activeBookings\",\n        url: \"/dashboard/active-bookings\",\n        icon: _barrel_optimize_names_BookOpen_Calendar_Car_ChevronRight_Clock_FileText_UserCheck_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        badge: \"12\",\n        description: \"Manage active rentals\"\n    },\n    {\n        title: \"nav.cars\",\n        url: \"/dashboard/cars\",\n        icon: _barrel_optimize_names_BookOpen_Calendar_Car_ChevronRight_Clock_FileText_UserCheck_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        description: \"Fleet management\"\n    },\n    {\n        title: \"nav.damageReports\",\n        url: \"/dashboard/damage-reports\",\n        icon: _barrel_optimize_names_BookOpen_Calendar_Car_ChevronRight_Clock_FileText_UserCheck_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        badge: \"2\",\n        description: \"Vehicle damage reports\"\n    },\n    {\n        title: \"nav.wallet\",\n        url: \"/dashboard/wallet\",\n        icon: _barrel_optimize_names_BookOpen_Calendar_Car_ChevronRight_Clock_FileText_UserCheck_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        description: \"Financial transactions\"\n    },\n    {\n        title: \"nav.clients\",\n        url: \"/dashboard/clients\",\n        icon: _barrel_optimize_names_BookOpen_Calendar_Car_ChevronRight_Clock_FileText_UserCheck_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        description: \"Client database\"\n    }\n];\nfunction AppSidebar() {\n    _s();\n    const { t } = (0,_components_language_provider__WEBPACK_IMPORTED_MODULE_3__.useLanguage)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n        delayDuration: 0,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.Sidebar, {\n            className: \"border-r-0 shadow-xl group-data-[collapsible=icon]:shadow-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarHeader, {\n                    className: \"border-b border-sidebar-border/50 bg-gradient-to-r from-sidebar-background to-sidebar-background/80\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 group-data-[collapsible=icon]:justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 group-data-[collapsible=icon]:gap-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 rounded-xl bg-gradient-to-br from-primary via-primary to-primary/80 flex items-center justify-center shadow-lg group-data-[collapsible=icon]:w-10 group-data-[collapsible=icon]:h-10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_ChevronRight_Clock_FileText_UserCheck_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white group-data-[collapsible=icon]:h-5 group-data-[collapsible=icon]:w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-1 -right-1 w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center shadow-md group-data-[collapsible=icon]:w-3 group-data-[collapsible=icon]:h-3 group-data-[collapsible=icon]:-top-0.5 group-data-[collapsible=icon]:-right-0.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-yellow-600 rounded-full group-data-[collapsible=icon]:w-1.5 group-data-[collapsible=icon]:h-1.5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group-data-[collapsible=icon]:hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent\",\n                                                children: \"OR_EL\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground font-medium\",\n                                                children: \"Rental Management\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarTrigger, {\n                                className: \"modern-button group-data-[collapsible=icon]:hidden\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarContent, {\n                    className: \"px-2 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarGroup, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarGroupLabel, {\n                                className: \"text-xs font-semibold text-muted-foreground/80 uppercase tracking-wider px-3 mb-2 group-data-[collapsible=icon]:hidden\",\n                                children: \"Navigation\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarGroupContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarMenu, {\n                                    className: \"space-y-1\",\n                                    children: menuItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarMenuItem, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarMenuButton, {\n                                                            asChild: true,\n                                                            isActive: pathname === item.url,\n                                                            className: \"modern-button h-12 px-3 rounded-xl hover:bg-sidebar-accent/80 data-[active=true]:bg-gradient-to-r data-[active=true]:from-primary data-[active=true]:to-primary/90 data-[active=true]:text-white data-[active=true]:shadow-lg group-data-[collapsible=icon]:justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: item.url,\n                                                                className: \"flex items-center justify-between w-full\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                                className: \"h-5 w-5 flex-shrink-0\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                                                                lineNumber: 122,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium group-data-[collapsible=icon]:hidden\",\n                                                                                children: t(item.title)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                                                                lineNumber: 123,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                                                        lineNumber: 121,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 group-data-[collapsible=icon]:hidden\",\n                                                                        children: [\n                                                                            item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                variant: \"secondary\",\n                                                                                className: \"h-5 px-2 text-xs font-semibold\",\n                                                                                children: item.badge\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                                                                lineNumber: 127,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Car_ChevronRight_Clock_FileText_UserCheck_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-4 w-4 opacity-50\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                                                                lineNumber: 131,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                                                        lineNumber: 125,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                                                lineNumber: 120,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipContent, {\n                                                        side: \"right\",\n                                                        className: \"group-data-[collapsible=expanded]:hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: t(item.title)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                                                    lineNumber: 138,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: item.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                                                    lineNumber: 139,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, item.title, false, {\n                                            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarFooter, {\n                    className: \"border-t border-sidebar-border/50 p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-xs text-muted-foreground group-data-[collapsible=icon]:hidden\",\n                        children: \"\\xa9 2024 OR_EL Rentals\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/OR_EL RENTAL/components/app-sidebar.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n_s(AppSidebar, \"BMKCxFc84scyPsJbKzkED4T3X4o=\", false, function() {\n    return [\n        _components_language_provider__WEBPACK_IMPORTED_MODULE_3__.useLanguage,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.usePathname\n    ];\n});\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/app-sidebar.tsx\n"));

/***/ })

});