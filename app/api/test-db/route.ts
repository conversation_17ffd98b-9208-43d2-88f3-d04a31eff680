import { NextRequest, NextResponse } from 'next/server'
import { dbConfig, connectionString, testConnection, query } from '@/lib/database'

export async function GET() {
  try {
    // Try to test the actual database connection
    const isConnected = await testConnection()

    if (isConnected) {
      // If connected, get some database info
      try {
        const result = await query('SELECT NOW() as current_time, version() as postgres_version')

        return NextResponse.json({
          success: true,
          message: 'Database connection successful!',
          data: {
            connected: true,
            currentTime: result.rows[0].current_time,
            postgresVersion: result.rows[0].postgres_version,
            config: {
              host: dbConfig.host,
              port: dbConfig.port,
              database: dbConfig.database,
              user: dbConfig.user,
              ssl: dbConfig.ssl
            }
          }
        })
      } catch (queryError) {
        return NextResponse.json({
          success: false,
          message: 'Connected but query failed',
          error: queryError instanceof Error ? queryError.message : 'Unknown query error'
        }, { status: 500 })
      }
    } else {
      // If not connected, return configuration info
      return NextResponse.json({
        success: false,
        message: 'Database connection failed - showing configuration',
        note: 'Install PostgreSQL driver: npm install pg @types/pg',
        data: {
          configured: true,
          connectionString: connectionString.replace(/:([^:@]+)@/, ':****@'), // Hide password
          config: {
            host: dbConfig.host,
            port: dbConfig.port,
            database: dbConfig.database,
            user: dbConfig.user,
            ssl: dbConfig.ssl
          },
          environment: {
            DB_HOST: process.env.DB_HOST,
            DB_PORT: process.env.DB_PORT,
            DB_NAME: process.env.DB_NAME,
            DB_USER: process.env.DB_USER,
            DATABASE_URL_SET: !!process.env.DATABASE_URL
          }
        }
      })
    }
  } catch (error) {
    console.error('Database test error:', error)
    return NextResponse.json(
      {
        error: 'Database test failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        note: 'Make sure to install: npm install pg @types/pg'
      },
      { status: 500 }
    )
  }
}

// POST endpoint - test database with a simple query
export async function POST() {
  try {
    const result = await query('SELECT 1 as test, NOW() as timestamp')

    return NextResponse.json({
      success: true,
      message: 'Database query test successful!',
      data: {
        testResult: result.rows[0],
        rowCount: result.rowCount,
        connected: true
      }
    })
  } catch (error) {
    console.error('Database POST test error:', error)
    return NextResponse.json({
      success: false,
      message: 'Database query test failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      instructions: [
        '1. Install PostgreSQL driver: npm install pg @types/pg',
        '2. Ensure your PostgreSQL database is running',
        '3. Check your .env configuration'
      ]
    }, { status: 500 })
  }
}
