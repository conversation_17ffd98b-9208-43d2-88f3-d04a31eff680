// Database configuration from environment variables
export const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'postgres',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
}

// Connection string for easy use
export const connectionString = process.env.DATABASE_URL ||
  `postgresql://${dbConfig.user}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`

// Check if pg package is available
function checkPgAvailability(): boolean {
  try {
    require.resolve('pg')
    return true
  } catch {
    return false
  }
}

// Create a connection pool with dynamic require
let pool: any = null

export function getPool(): any {
  const pgAvailable = checkPgAvailability()

  if (!pgAvailable) {
    throw new Error('PostgreSQL driver not installed. Run: npm install pg @types/pg')
  }

  if (!pool) {
    const { Pool } = require('pg')
    pool = new Pool(dbConfig)

    // Handle pool errors
    pool.on('error', (err: Error) => {
      console.error('Unexpected error on idle client', err)
      process.exit(-1)
    })
  }

  return pool
}

// Test database connection
export async function testConnection(): Promise<boolean> {
  try {
    const pgAvailable = checkPgAvailability()

    if (!pgAvailable) {
      console.log('❌ PostgreSQL driver not installed. Run: npm install pg @types/pg')
      return false
    }

    const pool = getPool()
    const client = await pool.connect()
    const result = await client.query('SELECT NOW()')
    client.release()
    console.log('✅ Database connected successfully at:', result.rows[0].now)
    return true
  } catch (error) {
    console.error('❌ Database connection failed:', error)
    return false
  }
}

// Execute a query
export async function query(text: string, params?: any[]): Promise<any> {
  const pgAvailable = checkPgAvailability()

  if (!pgAvailable) {
    throw new Error('PostgreSQL driver not installed. Run: npm install pg @types/pg')
  }

  const pool = getPool()
  const client = await pool.connect()

  try {
    const result = await client.query(text, params)
    return result
  } catch (error) {
    console.error('Database query error:', error)
    throw error
  } finally {
    client.release()
  }
}

// Close the database connection pool
export async function closePool(): Promise<void> {
  const pgAvailable = checkPgAvailability()

  if (!pgAvailable) {
    console.log('PostgreSQL driver not installed - nothing to close')
    return
  }

  if (pool) {
    await pool.end()
    pool = null
    console.log('Database pool closed')
  }
}

// Database utility functions
export const db = {
  query,
  testConnection,
  getPool,
  closePool,
}

export default db
