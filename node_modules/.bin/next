#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/bin/node_modules:/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/node_modules:/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/node_modules:/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules:/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/bin/node_modules:/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/node_modules:/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/node_modules:/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules:/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../next/dist/bin/next" "$@"
else
  exec node  "$basedir/../next/dist/bin/next" "$@"
fi
