{"version": 3, "sources": ["../../../../src/build/babel/plugins/next-ssg-transform.ts"], "sourcesContent": ["import type {\n  NodePath,\n  types as BabelTypes,\n} from 'next/dist/compiled/babel/core'\nimport type { PluginObj } from 'next/dist/compiled/babel/core'\nimport { SERVER_PROPS_SSG_CONFLICT } from '../../../lib/constants'\nimport { SERVER_PROPS_ID, STATIC_PROPS_ID } from '../../../shared/lib/constants'\n\nexport const EXPORT_NAME_GET_STATIC_PROPS = 'getStaticProps'\nexport const EXPORT_NAME_GET_STATIC_PATHS = 'getStaticPaths'\nexport const EXPORT_NAME_GET_SERVER_PROPS = 'getServerSideProps'\n\nconst ssgExports = new Set([\n  EXPORT_NAME_GET_STATIC_PROPS,\n  EXPORT_NAME_GET_STATIC_PATHS,\n  EXPORT_NAME_GET_SERVER_PROPS,\n\n  // legacy methods added so build doesn't fail from importing\n  // server-side only methods\n  `unstable_getStaticProps`,\n  `unstable_getStaticPaths`,\n  `unstable_getServerProps`,\n  `unstable_getServerSideProps`,\n])\n\ntype PluginState = {\n  refs: Set<NodePath<BabelTypes.Identifier>>\n  isPrerender: boolean\n  isServerProps: boolean\n  done: boolean\n}\n\nfunction decorateSsgExport(\n  t: typeof BabelTypes,\n  path: NodePath<BabelTypes.Program>,\n  state: PluginState\n): void {\n  const gsspName = state.isPrerender ? STATIC_PROPS_ID : SERVER_PROPS_ID\n  const gsspId = t.identifier(gsspName)\n\n  const addGsspExport = (\n    exportPath:\n      | NodePath<BabelTypes.ExportDefaultDeclaration>\n      | NodePath<BabelTypes.ExportNamedDeclaration>\n  ): void => {\n    if (state.done) {\n      return\n    }\n    state.done = true\n\n    const [pageCompPath] = exportPath.replaceWithMultiple([\n      t.exportNamedDeclaration(\n        t.variableDeclaration(\n          // We use 'var' instead of 'let' or 'const' for ES5 support. Since\n          // this runs in `Program#exit`, no ES2015 transforms (preset env)\n          // will be ran against this code.\n          'var',\n          [t.variableDeclarator(gsspId, t.booleanLiteral(true))]\n        ),\n        [t.exportSpecifier(gsspId, gsspId)]\n      ),\n      exportPath.node,\n    ])\n    exportPath.scope.registerDeclaration(\n      pageCompPath as NodePath<BabelTypes.Node>\n    )\n  }\n\n  path.traverse({\n    ExportDefaultDeclaration(exportDefaultPath) {\n      addGsspExport(exportDefaultPath)\n    },\n    ExportNamedDeclaration(exportNamedPath) {\n      addGsspExport(exportNamedPath)\n    },\n  })\n}\n\nconst isDataIdentifier = (name: string, state: PluginState): boolean => {\n  if (ssgExports.has(name)) {\n    if (name === EXPORT_NAME_GET_SERVER_PROPS) {\n      if (state.isPrerender) {\n        throw new Error(SERVER_PROPS_SSG_CONFLICT)\n      }\n      state.isServerProps = true\n    } else {\n      if (state.isServerProps) {\n        throw new Error(SERVER_PROPS_SSG_CONFLICT)\n      }\n      state.isPrerender = true\n    }\n    return true\n  }\n  return false\n}\n\nexport default function nextTransformSsg({\n  types: t,\n}: {\n  types: typeof BabelTypes\n}): PluginObj<PluginState> {\n  function getIdentifier(\n    path:\n      | NodePath<BabelTypes.FunctionDeclaration>\n      | NodePath<BabelTypes.FunctionExpression>\n      | NodePath<BabelTypes.ArrowFunctionExpression>\n  ): NodePath<BabelTypes.Identifier> | null {\n    const parentPath = path.parentPath\n    if (parentPath.type === 'VariableDeclarator') {\n      const pp = parentPath as NodePath<BabelTypes.VariableDeclarator>\n      const name = pp.get('id')\n      return name.node.type === 'Identifier'\n        ? (name as NodePath<BabelTypes.Identifier>)\n        : null\n    }\n\n    if (parentPath.type === 'AssignmentExpression') {\n      const pp = parentPath as NodePath<BabelTypes.AssignmentExpression>\n      const name = pp.get('left')\n      return name.node.type === 'Identifier'\n        ? (name as NodePath<BabelTypes.Identifier>)\n        : null\n    }\n\n    if (path.node.type === 'ArrowFunctionExpression') {\n      return null\n    }\n\n    return path.node.id && path.node.id.type === 'Identifier'\n      ? (path.get('id') as NodePath<BabelTypes.Identifier>)\n      : null\n  }\n\n  function isIdentifierReferenced(\n    ident: NodePath<BabelTypes.Identifier>\n  ): boolean {\n    const b = ident.scope.getBinding(ident.node.name)\n    if (b?.referenced) {\n      // Functions can reference themselves, so we need to check if there's a\n      // binding outside the function scope or not.\n      if (b.path.type === 'FunctionDeclaration') {\n        return !b.constantViolations\n          .concat(b.referencePaths)\n          // Check that every reference is contained within the function:\n          .every((ref) => ref.findParent((p) => p === b.path))\n      }\n\n      return true\n    }\n    return false\n  }\n\n  function markFunction(\n    path:\n      | NodePath<BabelTypes.FunctionDeclaration>\n      | NodePath<BabelTypes.FunctionExpression>\n      | NodePath<BabelTypes.ArrowFunctionExpression>,\n    state: PluginState\n  ): void {\n    const ident = getIdentifier(path)\n    if (ident?.node && isIdentifierReferenced(ident)) {\n      state.refs.add(ident)\n    }\n  }\n\n  function markImport(\n    path:\n      | NodePath<BabelTypes.ImportSpecifier>\n      | NodePath<BabelTypes.ImportDefaultSpecifier>\n      | NodePath<BabelTypes.ImportNamespaceSpecifier>,\n    state: PluginState\n  ): void {\n    const local = path.get('local') as NodePath<BabelTypes.Identifier>\n    if (isIdentifierReferenced(local)) {\n      state.refs.add(local)\n    }\n  }\n\n  return {\n    visitor: {\n      Program: {\n        enter(path, state) {\n          state.refs = new Set<NodePath<BabelTypes.Identifier>>()\n          state.isPrerender = false\n          state.isServerProps = false\n          state.done = false\n\n          path.traverse(\n            {\n              VariableDeclarator(variablePath, variableState) {\n                if (variablePath.node.id.type === 'Identifier') {\n                  const local = variablePath.get(\n                    'id'\n                  ) as NodePath<BabelTypes.Identifier>\n                  if (isIdentifierReferenced(local)) {\n                    variableState.refs.add(local)\n                  }\n                } else if (variablePath.node.id.type === 'ObjectPattern') {\n                  const pattern = variablePath.get(\n                    'id'\n                  ) as NodePath<BabelTypes.ObjectPattern>\n\n                  const properties = pattern.get('properties')\n                  properties.forEach((p) => {\n                    const local = p.get(\n                      p.node.type === 'ObjectProperty'\n                        ? 'value'\n                        : p.node.type === 'RestElement'\n                          ? 'argument'\n                          : (function () {\n                              throw new Error('invariant')\n                            })()\n                    ) as NodePath<BabelTypes.Identifier>\n                    if (isIdentifierReferenced(local)) {\n                      variableState.refs.add(local)\n                    }\n                  })\n                } else if (variablePath.node.id.type === 'ArrayPattern') {\n                  const pattern = variablePath.get(\n                    'id'\n                  ) as NodePath<BabelTypes.ArrayPattern>\n\n                  const elements = pattern.get('elements')\n                  elements.forEach((e) => {\n                    let local: NodePath<BabelTypes.Identifier>\n                    if (e.node?.type === 'Identifier') {\n                      local = e as NodePath<BabelTypes.Identifier>\n                    } else if (e.node?.type === 'RestElement') {\n                      local = e.get(\n                        'argument'\n                      ) as NodePath<BabelTypes.Identifier>\n                    } else {\n                      return\n                    }\n\n                    if (isIdentifierReferenced(local)) {\n                      variableState.refs.add(local)\n                    }\n                  })\n                }\n              },\n              FunctionDeclaration: markFunction,\n              FunctionExpression: markFunction,\n              ArrowFunctionExpression: markFunction,\n              ImportSpecifier: markImport,\n              ImportDefaultSpecifier: markImport,\n              ImportNamespaceSpecifier: markImport,\n              ExportNamedDeclaration(exportNamedPath, exportNamedState) {\n                const specifiers = exportNamedPath.get('specifiers')\n                if (specifiers.length) {\n                  specifiers.forEach((s) => {\n                    if (\n                      isDataIdentifier(\n                        t.isIdentifier(s.node.exported)\n                          ? s.node.exported.name\n                          : s.node.exported.value,\n                        exportNamedState\n                      )\n                    ) {\n                      s.remove()\n                    }\n                  })\n\n                  if (exportNamedPath.node.specifiers.length < 1) {\n                    exportNamedPath.remove()\n                  }\n                  return\n                }\n\n                const decl = exportNamedPath.get('declaration') as NodePath<\n                  | BabelTypes.FunctionDeclaration\n                  | BabelTypes.VariableDeclaration\n                >\n                if (decl == null || decl.node == null) {\n                  return\n                }\n\n                switch (decl.node.type) {\n                  case 'FunctionDeclaration': {\n                    const name = decl.node.id!.name\n                    if (isDataIdentifier(name, exportNamedState)) {\n                      exportNamedPath.remove()\n                    }\n                    break\n                  }\n                  case 'VariableDeclaration': {\n                    const inner = decl.get(\n                      'declarations'\n                    ) as NodePath<BabelTypes.VariableDeclarator>[]\n                    inner.forEach((d) => {\n                      if (d.node.id.type !== 'Identifier') {\n                        return\n                      }\n                      const name = d.node.id.name\n                      if (isDataIdentifier(name, exportNamedState)) {\n                        d.remove()\n                      }\n                    })\n                    break\n                  }\n                  default: {\n                    break\n                  }\n                }\n              },\n            },\n            state\n          )\n\n          if (!state.isPrerender && !state.isServerProps) {\n            return\n          }\n\n          const refs = state.refs\n          let count: number\n\n          function sweepFunction(\n            sweepPath:\n              | NodePath<BabelTypes.FunctionDeclaration>\n              | NodePath<BabelTypes.FunctionExpression>\n              | NodePath<BabelTypes.ArrowFunctionExpression>\n          ): void {\n            const ident = getIdentifier(sweepPath)\n            if (\n              ident?.node &&\n              refs.has(ident) &&\n              !isIdentifierReferenced(ident)\n            ) {\n              ++count\n\n              if (\n                t.isAssignmentExpression(sweepPath.parentPath.node) ||\n                t.isVariableDeclarator(sweepPath.parentPath.node)\n              ) {\n                sweepPath.parentPath.remove()\n              } else {\n                sweepPath.remove()\n              }\n            }\n          }\n\n          function sweepImport(\n            sweepPath:\n              | NodePath<BabelTypes.ImportSpecifier>\n              | NodePath<BabelTypes.ImportDefaultSpecifier>\n              | NodePath<BabelTypes.ImportNamespaceSpecifier>\n          ): void {\n            const local = sweepPath.get(\n              'local'\n            ) as NodePath<BabelTypes.Identifier>\n            if (refs.has(local) && !isIdentifierReferenced(local)) {\n              ++count\n              sweepPath.remove()\n              if (\n                (sweepPath.parent as BabelTypes.ImportDeclaration).specifiers\n                  .length === 0\n              ) {\n                sweepPath.parentPath.remove()\n              }\n            }\n          }\n\n          do {\n            ;(path.scope as any).crawl()\n            count = 0\n\n            path.traverse({\n              // eslint-disable-next-line no-loop-func\n              VariableDeclarator(variablePath) {\n                if (variablePath.node.id.type === 'Identifier') {\n                  const local = variablePath.get(\n                    'id'\n                  ) as NodePath<BabelTypes.Identifier>\n                  if (refs.has(local) && !isIdentifierReferenced(local)) {\n                    ++count\n                    variablePath.remove()\n                  }\n                } else if (variablePath.node.id.type === 'ObjectPattern') {\n                  const pattern = variablePath.get(\n                    'id'\n                  ) as NodePath<BabelTypes.ObjectPattern>\n\n                  const beforeCount = count\n                  const properties = pattern.get('properties')\n                  properties.forEach((p) => {\n                    const local = p.get(\n                      p.node.type === 'ObjectProperty'\n                        ? 'value'\n                        : p.node.type === 'RestElement'\n                          ? 'argument'\n                          : (function () {\n                              throw new Error('invariant')\n                            })()\n                    ) as NodePath<BabelTypes.Identifier>\n\n                    if (refs.has(local) && !isIdentifierReferenced(local)) {\n                      ++count\n                      p.remove()\n                    }\n                  })\n\n                  if (\n                    beforeCount !== count &&\n                    pattern.get('properties').length < 1\n                  ) {\n                    variablePath.remove()\n                  }\n                } else if (variablePath.node.id.type === 'ArrayPattern') {\n                  const pattern = variablePath.get(\n                    'id'\n                  ) as NodePath<BabelTypes.ArrayPattern>\n\n                  const beforeCount = count\n                  const elements = pattern.get('elements')\n                  elements.forEach((e) => {\n                    let local: NodePath<BabelTypes.Identifier>\n                    if (e.node?.type === 'Identifier') {\n                      local = e as NodePath<BabelTypes.Identifier>\n                    } else if (e.node?.type === 'RestElement') {\n                      local = e.get(\n                        'argument'\n                      ) as NodePath<BabelTypes.Identifier>\n                    } else {\n                      return\n                    }\n\n                    if (refs.has(local) && !isIdentifierReferenced(local)) {\n                      ++count\n                      e.remove()\n                    }\n                  })\n\n                  if (\n                    beforeCount !== count &&\n                    pattern.get('elements').length < 1\n                  ) {\n                    variablePath.remove()\n                  }\n                }\n              },\n              FunctionDeclaration: sweepFunction,\n              FunctionExpression: sweepFunction,\n              ArrowFunctionExpression: sweepFunction,\n              ImportSpecifier: sweepImport,\n              ImportDefaultSpecifier: sweepImport,\n              ImportNamespaceSpecifier: sweepImport,\n            })\n          } while (count)\n\n          decorateSsgExport(t, path, state)\n        },\n      },\n    },\n  }\n}\n"], "names": ["EXPORT_NAME_GET_SERVER_PROPS", "EXPORT_NAME_GET_STATIC_PATHS", "EXPORT_NAME_GET_STATIC_PROPS", "nextTransformSsg", "ssgExports", "Set", "decorateSsgExport", "t", "path", "state", "gsspName", "<PERSON><PERSON><PERSON><PERSON>", "STATIC_PROPS_ID", "SERVER_PROPS_ID", "gsspId", "identifier", "addGsspExport", "exportPath", "done", "pageCompPath", "replaceWithMultiple", "exportNamedDeclaration", "variableDeclaration", "variableDeclarator", "booleanLiteral", "exportSpecifier", "node", "scope", "registerDeclaration", "traverse", "ExportDefaultDeclaration", "exportDefaultPath", "ExportNamedDeclaration", "exportNamedPath", "isDataIdentifier", "name", "has", "Error", "SERVER_PROPS_SSG_CONFLICT", "isServerProps", "types", "getIdentifier", "parentPath", "type", "pp", "get", "id", "isIdentifierReferenced", "ident", "b", "getBinding", "referenced", "constantViolations", "concat", "referencePaths", "every", "ref", "findParent", "p", "markFunction", "refs", "add", "markImport", "local", "visitor", "Program", "enter", "VariableDeclarator", "variablePath", "variableState", "pattern", "properties", "for<PERSON>ach", "elements", "e", "FunctionDeclaration", "FunctionExpression", "ArrowFunctionExpression", "ImportSpecifier", "ImportDefaultSpecifier", "ImportNamespaceSpecifier", "exportNamedState", "specifiers", "length", "s", "isIdentifier", "exported", "value", "remove", "decl", "inner", "d", "count", "sweepFunction", "sweepPath", "isAssignmentExpression", "isVariableDeclarator", "sweepImport", "parent", "crawl", "beforeCount"], "mappings": ";;;;;;;;;;;;;;;;;IAUaA,4BAA4B;eAA5BA;;IADAC,4BAA4B;eAA5BA;;IADAC,4BAA4B;eAA5BA;;IAwFb,OAsWC;eAtWuBC;;;2BA3FkB;4BACO;AAE1C,MAAMD,+BAA+B;AACrC,MAAMD,+BAA+B;AACrC,MAAMD,+BAA+B;AAE5C,MAAMI,aAAa,IAAIC,IAAI;IACzBH;IACAD;IACAD;IAEA,4DAA4D;IAC5D,2BAA2B;IAC3B,CAAC,uBAAuB,CAAC;IACzB,CAAC,uBAAuB,CAAC;IACzB,CAAC,uBAAuB,CAAC;IACzB,CAAC,2BAA2B,CAAC;CAC9B;AASD,SAASM,kBACPC,CAAoB,EACpBC,IAAkC,EAClCC,KAAkB;IAElB,MAAMC,WAAWD,MAAME,WAAW,GAAGC,2BAAe,GAAGC,2BAAe;IACtE,MAAMC,SAASP,EAAEQ,UAAU,CAACL;IAE5B,MAAMM,gBAAgB,CACpBC;QAIA,IAAIR,MAAMS,IAAI,EAAE;YACd;QACF;QACAT,MAAMS,IAAI,GAAG;QAEb,MAAM,CAACC,aAAa,GAAGF,WAAWG,mBAAmB,CAAC;YACpDb,EAAEc,sBAAsB,CACtBd,EAAEe,mBAAmB,CACnB,kEAAkE;YAClE,iEAAiE;YACjE,iCAAiC;YACjC,OACA;gBAACf,EAAEgB,kBAAkB,CAACT,QAAQP,EAAEiB,cAAc,CAAC;aAAO,GAExD;gBAACjB,EAAEkB,eAAe,CAACX,QAAQA;aAAQ;YAErCG,WAAWS,IAAI;SAChB;QACDT,WAAWU,KAAK,CAACC,mBAAmB,CAClCT;IAEJ;IAEAX,KAAKqB,QAAQ,CAAC;QACZC,0BAAyBC,iBAAiB;YACxCf,cAAce;QAChB;QACAC,wBAAuBC,eAAe;YACpCjB,cAAciB;QAChB;IACF;AACF;AAEA,MAAMC,mBAAmB,CAACC,MAAc1B;IACtC,IAAIL,WAAWgC,GAAG,CAACD,OAAO;QACxB,IAAIA,SAASnC,8BAA8B;YACzC,IAAIS,MAAME,WAAW,EAAE;gBACrB,MAAM,qBAAoC,CAApC,IAAI0B,MAAMC,oCAAyB,GAAnC,qBAAA;2BAAA;gCAAA;kCAAA;gBAAmC;YAC3C;YACA7B,MAAM8B,aAAa,GAAG;QACxB,OAAO;YACL,IAAI9B,MAAM8B,aAAa,EAAE;gBACvB,MAAM,qBAAoC,CAApC,IAAIF,MAAMC,oCAAyB,GAAnC,qBAAA;2BAAA;gCAAA;kCAAA;gBAAmC;YAC3C;YACA7B,MAAME,WAAW,GAAG;QACtB;QACA,OAAO;IACT;IACA,OAAO;AACT;AAEe,SAASR,iBAAiB,EACvCqC,OAAOjC,CAAC,EAGT;IACC,SAASkC,cACPjC,IAGgD;QAEhD,MAAMkC,aAAalC,KAAKkC,UAAU;QAClC,IAAIA,WAAWC,IAAI,KAAK,sBAAsB;YAC5C,MAAMC,KAAKF;YACX,MAAMP,OAAOS,GAAGC,GAAG,CAAC;YACpB,OAAOV,KAAKT,IAAI,CAACiB,IAAI,KAAK,eACrBR,OACD;QACN;QAEA,IAAIO,WAAWC,IAAI,KAAK,wBAAwB;YAC9C,MAAMC,KAAKF;YACX,MAAMP,OAAOS,GAAGC,GAAG,CAAC;YACpB,OAAOV,KAAKT,IAAI,CAACiB,IAAI,KAAK,eACrBR,OACD;QACN;QAEA,IAAI3B,KAAKkB,IAAI,CAACiB,IAAI,KAAK,2BAA2B;YAChD,OAAO;QACT;QAEA,OAAOnC,KAAKkB,IAAI,CAACoB,EAAE,IAAItC,KAAKkB,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,eACxCnC,KAAKqC,GAAG,CAAC,QACV;IACN;IAEA,SAASE,uBACPC,KAAsC;QAEtC,MAAMC,IAAID,MAAMrB,KAAK,CAACuB,UAAU,CAACF,MAAMtB,IAAI,CAACS,IAAI;QAChD,IAAIc,qBAAAA,EAAGE,UAAU,EAAE;YACjB,uEAAuE;YACvE,6CAA6C;YAC7C,IAAIF,EAAEzC,IAAI,CAACmC,IAAI,KAAK,uBAAuB;gBACzC,OAAO,CAACM,EAAEG,kBAAkB,CACzBC,MAAM,CAACJ,EAAEK,cAAc,CACxB,+DAA+D;iBAC9DC,KAAK,CAAC,CAACC,MAAQA,IAAIC,UAAU,CAAC,CAACC,IAAMA,MAAMT,EAAEzC,IAAI;YACtD;YAEA,OAAO;QACT;QACA,OAAO;IACT;IAEA,SAASmD,aACPnD,IAGgD,EAChDC,KAAkB;QAElB,MAAMuC,QAAQP,cAAcjC;QAC5B,IAAIwC,CAAAA,yBAAAA,MAAOtB,IAAI,KAAIqB,uBAAuBC,QAAQ;YAChDvC,MAAMmD,IAAI,CAACC,GAAG,CAACb;QACjB;IACF;IAEA,SAASc,WACPtD,IAGiD,EACjDC,KAAkB;QAElB,MAAMsD,QAAQvD,KAAKqC,GAAG,CAAC;QACvB,IAAIE,uBAAuBgB,QAAQ;YACjCtD,MAAMmD,IAAI,CAACC,GAAG,CAACE;QACjB;IACF;IAEA,OAAO;QACLC,SAAS;YACPC,SAAS;gBACPC,OAAM1D,IAAI,EAAEC,KAAK;oBACfA,MAAMmD,IAAI,GAAG,IAAIvD;oBACjBI,MAAME,WAAW,GAAG;oBACpBF,MAAM8B,aAAa,GAAG;oBACtB9B,MAAMS,IAAI,GAAG;oBAEbV,KAAKqB,QAAQ,CACX;wBACEsC,oBAAmBC,YAAY,EAAEC,aAAa;4BAC5C,IAAID,aAAa1C,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,cAAc;gCAC9C,MAAMoB,QAAQK,aAAavB,GAAG,CAC5B;gCAEF,IAAIE,uBAAuBgB,QAAQ;oCACjCM,cAAcT,IAAI,CAACC,GAAG,CAACE;gCACzB;4BACF,OAAO,IAAIK,aAAa1C,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,iBAAiB;gCACxD,MAAM2B,UAAUF,aAAavB,GAAG,CAC9B;gCAGF,MAAM0B,aAAaD,QAAQzB,GAAG,CAAC;gCAC/B0B,WAAWC,OAAO,CAAC,CAACd;oCAClB,MAAMK,QAAQL,EAAEb,GAAG,CACjBa,EAAEhC,IAAI,CAACiB,IAAI,KAAK,mBACZ,UACAe,EAAEhC,IAAI,CAACiB,IAAI,KAAK,gBACd,aACA,AAAC;wCACC,MAAM,qBAAsB,CAAtB,IAAIN,MAAM,cAAV,qBAAA;mDAAA;wDAAA;0DAAA;wCAAqB;oCAC7B;oCAER,IAAIU,uBAAuBgB,QAAQ;wCACjCM,cAAcT,IAAI,CAACC,GAAG,CAACE;oCACzB;gCACF;4BACF,OAAO,IAAIK,aAAa1C,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,gBAAgB;gCACvD,MAAM2B,UAAUF,aAAavB,GAAG,CAC9B;gCAGF,MAAM4B,WAAWH,QAAQzB,GAAG,CAAC;gCAC7B4B,SAASD,OAAO,CAAC,CAACE;wCAEZA,SAEOA;oCAHX,IAAIX;oCACJ,IAAIW,EAAAA,UAAAA,EAAEhD,IAAI,qBAANgD,QAAQ/B,IAAI,MAAK,cAAc;wCACjCoB,QAAQW;oCACV,OAAO,IAAIA,EAAAA,WAAAA,EAAEhD,IAAI,qBAANgD,SAAQ/B,IAAI,MAAK,eAAe;wCACzCoB,QAAQW,EAAE7B,GAAG,CACX;oCAEJ,OAAO;wCACL;oCACF;oCAEA,IAAIE,uBAAuBgB,QAAQ;wCACjCM,cAAcT,IAAI,CAACC,GAAG,CAACE;oCACzB;gCACF;4BACF;wBACF;wBACAY,qBAAqBhB;wBACrBiB,oBAAoBjB;wBACpBkB,yBAAyBlB;wBACzBmB,iBAAiBhB;wBACjBiB,wBAAwBjB;wBACxBkB,0BAA0BlB;wBAC1B9B,wBAAuBC,eAAe,EAAEgD,gBAAgB;4BACtD,MAAMC,aAAajD,gBAAgBY,GAAG,CAAC;4BACvC,IAAIqC,WAAWC,MAAM,EAAE;gCACrBD,WAAWV,OAAO,CAAC,CAACY;oCAClB,IACElD,iBACE3B,EAAE8E,YAAY,CAACD,EAAE1D,IAAI,CAAC4D,QAAQ,IAC1BF,EAAE1D,IAAI,CAAC4D,QAAQ,CAACnD,IAAI,GACpBiD,EAAE1D,IAAI,CAAC4D,QAAQ,CAACC,KAAK,EACzBN,mBAEF;wCACAG,EAAEI,MAAM;oCACV;gCACF;gCAEA,IAAIvD,gBAAgBP,IAAI,CAACwD,UAAU,CAACC,MAAM,GAAG,GAAG;oCAC9ClD,gBAAgBuD,MAAM;gCACxB;gCACA;4BACF;4BAEA,MAAMC,OAAOxD,gBAAgBY,GAAG,CAAC;4BAIjC,IAAI4C,QAAQ,QAAQA,KAAK/D,IAAI,IAAI,MAAM;gCACrC;4BACF;4BAEA,OAAQ+D,KAAK/D,IAAI,CAACiB,IAAI;gCACpB,KAAK;oCAAuB;wCAC1B,MAAMR,OAAOsD,KAAK/D,IAAI,CAACoB,EAAE,CAAEX,IAAI;wCAC/B,IAAID,iBAAiBC,MAAM8C,mBAAmB;4CAC5ChD,gBAAgBuD,MAAM;wCACxB;wCACA;oCACF;gCACA,KAAK;oCAAuB;wCAC1B,MAAME,QAAQD,KAAK5C,GAAG,CACpB;wCAEF6C,MAAMlB,OAAO,CAAC,CAACmB;4CACb,IAAIA,EAAEjE,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,cAAc;gDACnC;4CACF;4CACA,MAAMR,OAAOwD,EAAEjE,IAAI,CAACoB,EAAE,CAACX,IAAI;4CAC3B,IAAID,iBAAiBC,MAAM8C,mBAAmB;gDAC5CU,EAAEH,MAAM;4CACV;wCACF;wCACA;oCACF;gCACA;oCAAS;wCACP;oCACF;4BACF;wBACF;oBACF,GACA/E;oBAGF,IAAI,CAACA,MAAME,WAAW,IAAI,CAACF,MAAM8B,aAAa,EAAE;wBAC9C;oBACF;oBAEA,MAAMqB,OAAOnD,MAAMmD,IAAI;oBACvB,IAAIgC;oBAEJ,SAASC,cACPC,SAGgD;wBAEhD,MAAM9C,QAAQP,cAAcqD;wBAC5B,IACE9C,CAAAA,yBAAAA,MAAOtB,IAAI,KACXkC,KAAKxB,GAAG,CAACY,UACT,CAACD,uBAAuBC,QACxB;4BACA,EAAE4C;4BAEF,IACErF,EAAEwF,sBAAsB,CAACD,UAAUpD,UAAU,CAAChB,IAAI,KAClDnB,EAAEyF,oBAAoB,CAACF,UAAUpD,UAAU,CAAChB,IAAI,GAChD;gCACAoE,UAAUpD,UAAU,CAAC8C,MAAM;4BAC7B,OAAO;gCACLM,UAAUN,MAAM;4BAClB;wBACF;oBACF;oBAEA,SAASS,YACPH,SAGiD;wBAEjD,MAAM/B,QAAQ+B,UAAUjD,GAAG,CACzB;wBAEF,IAAIe,KAAKxB,GAAG,CAAC2B,UAAU,CAAChB,uBAAuBgB,QAAQ;4BACrD,EAAE6B;4BACFE,UAAUN,MAAM;4BAChB,IACE,AAACM,UAAUI,MAAM,CAAkChB,UAAU,CAC1DC,MAAM,KAAK,GACd;gCACAW,UAAUpD,UAAU,CAAC8C,MAAM;4BAC7B;wBACF;oBACF;oBAEA,GAAG;;wBACChF,KAAKmB,KAAK,CAASwE,KAAK;wBAC1BP,QAAQ;wBAERpF,KAAKqB,QAAQ,CAAC;4BACZ,wCAAwC;4BACxCsC,oBAAmBC,YAAY;gCAC7B,IAAIA,aAAa1C,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,cAAc;oCAC9C,MAAMoB,QAAQK,aAAavB,GAAG,CAC5B;oCAEF,IAAIe,KAAKxB,GAAG,CAAC2B,UAAU,CAAChB,uBAAuBgB,QAAQ;wCACrD,EAAE6B;wCACFxB,aAAaoB,MAAM;oCACrB;gCACF,OAAO,IAAIpB,aAAa1C,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,iBAAiB;oCACxD,MAAM2B,UAAUF,aAAavB,GAAG,CAC9B;oCAGF,MAAMuD,cAAcR;oCACpB,MAAMrB,aAAaD,QAAQzB,GAAG,CAAC;oCAC/B0B,WAAWC,OAAO,CAAC,CAACd;wCAClB,MAAMK,QAAQL,EAAEb,GAAG,CACjBa,EAAEhC,IAAI,CAACiB,IAAI,KAAK,mBACZ,UACAe,EAAEhC,IAAI,CAACiB,IAAI,KAAK,gBACd,aACA,AAAC;4CACC,MAAM,qBAAsB,CAAtB,IAAIN,MAAM,cAAV,qBAAA;uDAAA;4DAAA;8DAAA;4CAAqB;wCAC7B;wCAGR,IAAIuB,KAAKxB,GAAG,CAAC2B,UAAU,CAAChB,uBAAuBgB,QAAQ;4CACrD,EAAE6B;4CACFlC,EAAE8B,MAAM;wCACV;oCACF;oCAEA,IACEY,gBAAgBR,SAChBtB,QAAQzB,GAAG,CAAC,cAAcsC,MAAM,GAAG,GACnC;wCACAf,aAAaoB,MAAM;oCACrB;gCACF,OAAO,IAAIpB,aAAa1C,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,gBAAgB;oCACvD,MAAM2B,UAAUF,aAAavB,GAAG,CAC9B;oCAGF,MAAMuD,cAAcR;oCACpB,MAAMnB,WAAWH,QAAQzB,GAAG,CAAC;oCAC7B4B,SAASD,OAAO,CAAC,CAACE;4CAEZA,SAEOA;wCAHX,IAAIX;wCACJ,IAAIW,EAAAA,UAAAA,EAAEhD,IAAI,qBAANgD,QAAQ/B,IAAI,MAAK,cAAc;4CACjCoB,QAAQW;wCACV,OAAO,IAAIA,EAAAA,WAAAA,EAAEhD,IAAI,qBAANgD,SAAQ/B,IAAI,MAAK,eAAe;4CACzCoB,QAAQW,EAAE7B,GAAG,CACX;wCAEJ,OAAO;4CACL;wCACF;wCAEA,IAAIe,KAAKxB,GAAG,CAAC2B,UAAU,CAAChB,uBAAuBgB,QAAQ;4CACrD,EAAE6B;4CACFlB,EAAEc,MAAM;wCACV;oCACF;oCAEA,IACEY,gBAAgBR,SAChBtB,QAAQzB,GAAG,CAAC,YAAYsC,MAAM,GAAG,GACjC;wCACAf,aAAaoB,MAAM;oCACrB;gCACF;4BACF;4BACAb,qBAAqBkB;4BACrBjB,oBAAoBiB;4BACpBhB,yBAAyBgB;4BACzBf,iBAAiBmB;4BACjBlB,wBAAwBkB;4BACxBjB,0BAA0BiB;wBAC5B;oBACF,QAASL,OAAM;oBAEftF,kBAAkBC,GAAGC,MAAMC;gBAC7B;YACF;QACF;IACF;AACF"}