{"version": 3, "sources": ["../../src/build/handle-entrypoints.ts"], "sourcesContent": ["import type { CustomRoutes } from '../lib/load-custom-routes'\nimport type { TurbopackManifestLoader } from '../shared/lib/turbopack/manifest-loader'\nimport type {\n  TurbopackResult,\n  RawEntrypoints,\n  Entrypoints,\n  PageRoute,\n  AppRoute,\n} from './swc/types'\nimport * as Log from './output/log'\nimport { getEntryKey } from '../shared/lib/turbopack/entry-key'\nimport {\n  processIssues,\n  type EntryIssuesMap,\n} from '../shared/lib/turbopack/utils'\n\nexport async function handleEntrypoints({\n  entrypoints,\n  currentEntrypoints,\n  currentEntryIssues,\n  manifestLoader,\n  productionRewrites,\n  logErrors,\n}: {\n  entrypoints: TurbopackResult<RawEntrypoints>\n  currentEntrypoints: Entrypoints\n  currentEntryIssues: EntryIssuesMap\n  manifestLoader: TurbopackManifestLoader\n  productionRewrites: CustomRoutes['rewrites'] | undefined\n  logErrors: boolean\n}) {\n  currentEntrypoints.global.app = entrypoints.pagesAppEndpoint\n  currentEntrypoints.global.document = entrypoints.pagesDocumentEndpoint\n  currentEntrypoints.global.error = entrypoints.pagesErrorEndpoint\n\n  currentEntrypoints.global.instrumentation = entrypoints.instrumentation\n\n  currentEntrypoints.page.clear()\n  currentEntrypoints.app.clear()\n\n  for (const [pathname, route] of entrypoints.routes) {\n    switch (route.type) {\n      case 'page':\n      case 'page-api':\n        currentEntrypoints.page.set(pathname, route)\n        break\n      case 'app-page': {\n        route.pages.forEach((page) => {\n          currentEntrypoints.app.set(page.originalName, {\n            type: 'app-page',\n            ...page,\n          })\n        })\n        break\n      }\n      case 'app-route': {\n        currentEntrypoints.app.set(route.originalName, route)\n        break\n      }\n      default:\n        Log.info(`skipping ${pathname} (${route.type})`)\n        break\n    }\n  }\n\n  const { middleware, instrumentation } = entrypoints\n\n  // We check for explicit true/false, since it's initialized to\n  // undefined during the first loop (middlewareChanges event is\n  // unnecessary during the first serve)\n  if (currentEntrypoints.global.middleware && !middleware) {\n    const key = getEntryKey('root', 'server', 'middleware')\n    // Went from middleware to no middleware\n    currentEntryIssues.delete(key)\n  }\n\n  currentEntrypoints.global.middleware = middleware\n\n  if (instrumentation) {\n    const processInstrumentation = async (\n      name: string,\n      prop: 'nodeJs' | 'edge'\n    ) => {\n      const key = getEntryKey('root', 'server', name)\n\n      const writtenEndpoint = await instrumentation[prop].writeToDisk()\n      processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n    }\n    await processInstrumentation('instrumentation.nodeJs', 'nodeJs')\n    await processInstrumentation('instrumentation.edge', 'edge')\n    await manifestLoader.loadMiddlewareManifest(\n      'instrumentation',\n      'instrumentation'\n    )\n    await manifestLoader.writeManifests({\n      devRewrites: undefined,\n      productionRewrites,\n      entrypoints: currentEntrypoints,\n    })\n  }\n\n  if (middleware) {\n    const key = getEntryKey('root', 'server', 'middleware')\n\n    const endpoint = middleware.endpoint\n\n    async function processMiddleware() {\n      const writtenEndpoint = await endpoint.writeToDisk()\n      processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n      await manifestLoader.loadMiddlewareManifest('middleware', 'middleware')\n    }\n    await processMiddleware()\n  } else {\n    manifestLoader.deleteMiddlewareManifest(\n      getEntryKey('root', 'server', 'middleware')\n    )\n  }\n}\n\nexport async function handlePagesErrorRoute({\n  currentEntryIssues,\n  entrypoints,\n  manifestLoader,\n  productionRewrites,\n  logErrors,\n}: {\n  currentEntryIssues: EntryIssuesMap\n  entrypoints: Entrypoints\n  manifestLoader: TurbopackManifestLoader\n  productionRewrites: CustomRoutes['rewrites'] | undefined\n  logErrors: boolean\n}) {\n  if (entrypoints.global.app) {\n    const key = getEntryKey('pages', 'server', '_app')\n    const writtenEndpoint = await entrypoints.global.app.writeToDisk()\n    processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n  }\n  await manifestLoader.loadBuildManifest('_app')\n  await manifestLoader.loadPagesManifest('_app')\n  await manifestLoader.loadFontManifest('_app')\n\n  if (entrypoints.global.document) {\n    const key = getEntryKey('pages', 'server', '_document')\n    const writtenEndpoint = await entrypoints.global.document.writeToDisk()\n    processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n  }\n  await manifestLoader.loadPagesManifest('_document')\n\n  if (entrypoints.global.error) {\n    const key = getEntryKey('pages', 'server', '_error')\n    const writtenEndpoint = await entrypoints.global.error.writeToDisk()\n    processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n  }\n\n  await manifestLoader.loadBuildManifest('_error')\n  await manifestLoader.loadPagesManifest('_error')\n  await manifestLoader.loadFontManifest('_error')\n\n  await manifestLoader.writeManifests({\n    devRewrites: undefined,\n    productionRewrites,\n    entrypoints,\n  })\n}\n\nexport async function handleRouteType({\n  page,\n  route,\n  currentEntryIssues,\n  entrypoints,\n  manifestLoader,\n  productionRewrites,\n  logErrors,\n}: {\n  page: string\n  route: PageRoute | AppRoute\n\n  currentEntryIssues: EntryIssuesMap\n  entrypoints: Entrypoints\n  manifestLoader: TurbopackManifestLoader\n  productionRewrites: CustomRoutes['rewrites'] | undefined\n  logErrors: boolean\n}) {\n  const shouldCreateWebpackStats = process.env.TURBOPACK_STATS != null\n\n  switch (route.type) {\n    case 'page': {\n      const serverKey = getEntryKey('pages', 'server', page)\n\n      if (entrypoints.global.app) {\n        const key = getEntryKey('pages', 'server', '_app')\n\n        const writtenEndpoint = await entrypoints.global.app.writeToDisk()\n        processIssues(\n          currentEntryIssues,\n          key,\n          writtenEndpoint,\n          false,\n          logErrors\n        )\n      }\n      await manifestLoader.loadBuildManifest('_app')\n      await manifestLoader.loadPagesManifest('_app')\n\n      if (entrypoints.global.document) {\n        const key = getEntryKey('pages', 'server', '_document')\n\n        const writtenEndpoint = await entrypoints.global.document.writeToDisk()\n        processIssues(\n          currentEntryIssues,\n          key,\n          writtenEndpoint,\n          false,\n          logErrors\n        )\n      }\n      await manifestLoader.loadPagesManifest('_document')\n\n      const writtenEndpoint = await route.htmlEndpoint.writeToDisk()\n\n      const type = writtenEndpoint?.type\n\n      await manifestLoader.loadBuildManifest(page)\n      await manifestLoader.loadPagesManifest(page)\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'pages')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(serverKey)\n      }\n      await manifestLoader.loadFontManifest('/_app', 'pages')\n      await manifestLoader.loadFontManifest(page, 'pages')\n\n      if (shouldCreateWebpackStats) {\n        await manifestLoader.loadWebpackStats(page, 'pages')\n      }\n\n      await manifestLoader.writeManifests({\n        devRewrites: undefined,\n        productionRewrites,\n        entrypoints,\n      })\n\n      processIssues(\n        currentEntryIssues,\n        serverKey,\n        writtenEndpoint,\n        false,\n        logErrors\n      )\n\n      break\n    }\n    case 'page-api': {\n      const key = getEntryKey('pages', 'server', page)\n\n      const writtenEndpoint = await route.endpoint.writeToDisk()\n\n      const type = writtenEndpoint.type\n\n      await manifestLoader.loadPagesManifest(page)\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'pages')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.writeManifests({\n        devRewrites: undefined,\n        productionRewrites,\n        entrypoints,\n      })\n\n      processIssues(currentEntryIssues, key, writtenEndpoint, true, logErrors)\n\n      break\n    }\n    case 'app-page': {\n      const key = getEntryKey('app', 'server', page)\n      const writtenEndpoint = await route.htmlEndpoint.writeToDisk()\n      const type = writtenEndpoint.type\n\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'app')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.loadAppBuildManifest(page)\n      await manifestLoader.loadBuildManifest(page, 'app')\n      await manifestLoader.loadAppPathsManifest(page)\n      await manifestLoader.loadActionManifest(page)\n      await manifestLoader.loadFontManifest(page, 'app')\n\n      if (shouldCreateWebpackStats) {\n        await manifestLoader.loadWebpackStats(page, 'app')\n      }\n\n      await manifestLoader.writeManifests({\n        devRewrites: undefined,\n        productionRewrites,\n        entrypoints,\n      })\n\n      processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n\n      break\n    }\n    case 'app-route': {\n      const key = getEntryKey('app', 'server', page)\n      const writtenEndpoint = await route.endpoint.writeToDisk()\n      const type = writtenEndpoint.type\n\n      await manifestLoader.loadAppPathsManifest(page)\n\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'app')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.writeManifests({\n        devRewrites: undefined,\n        productionRewrites,\n        entrypoints,\n      })\n      processIssues(currentEntryIssues, key, writtenEndpoint, true, logErrors)\n\n      break\n    }\n    default: {\n      throw new Error(`unknown route type ${(route as any).type} for ${page}`)\n    }\n  }\n}\n"], "names": ["handleEntrypoints", "handlePagesErrorRoute", "handleRouteType", "entrypoints", "currentEntrypoints", "currentEntryIssues", "manifest<PERSON><PERSON>der", "productionRewrites", "logErrors", "global", "app", "pagesAppEndpoint", "document", "pagesDocumentEndpoint", "error", "pagesErrorEndpoint", "instrumentation", "page", "clear", "pathname", "route", "routes", "type", "set", "pages", "for<PERSON>ach", "originalName", "Log", "info", "middleware", "key", "getEntry<PERSON>ey", "delete", "processInstrumentation", "name", "prop", "writtenEndpoint", "writeToDisk", "processIssues", "loadMiddlewareManifest", "writeManifests", "devRewrites", "undefined", "endpoint", "processMiddleware", "deleteMiddlewareManifest", "loadBuildManifest", "loadPagesManifest", "loadFontManifest", "shouldCreateWebpackStats", "process", "env", "TURBOPACK_STATS", "server<PERSON>ey", "htmlEndpoint", "loadWebpackStats", "loadAppBuildManifest", "loadAppPathsManifest", "loadActionManifest", "Error"], "mappings": ";;;;;;;;;;;;;;;;IAgBsBA,iBAAiB;eAAjBA;;IAuGAC,qBAAqB;eAArBA;;IA8CAC,eAAe;eAAfA;;;6DA5JD;0BACO;uBAIrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,eAAeF,kBAAkB,EACtCG,WAAW,EACXC,kBAAkB,EAClBC,kBAAkB,EAClBC,cAAc,EACdC,kBAAkB,EAClBC,SAAS,EAQV;IACCJ,mBAAmBK,MAAM,CAACC,GAAG,GAAGP,YAAYQ,gBAAgB;IAC5DP,mBAAmBK,MAAM,CAACG,QAAQ,GAAGT,YAAYU,qBAAqB;IACtET,mBAAmBK,MAAM,CAACK,KAAK,GAAGX,YAAYY,kBAAkB;IAEhEX,mBAAmBK,MAAM,CAACO,eAAe,GAAGb,YAAYa,eAAe;IAEvEZ,mBAAmBa,IAAI,CAACC,KAAK;IAC7Bd,mBAAmBM,GAAG,CAACQ,KAAK;IAE5B,KAAK,MAAM,CAACC,UAAUC,MAAM,IAAIjB,YAAYkB,MAAM,CAAE;QAClD,OAAQD,MAAME,IAAI;YAChB,KAAK;YACL,KAAK;gBACHlB,mBAAmBa,IAAI,CAACM,GAAG,CAACJ,UAAUC;gBACtC;YACF,KAAK;gBAAY;oBACfA,MAAMI,KAAK,CAACC,OAAO,CAAC,CAACR;wBACnBb,mBAAmBM,GAAG,CAACa,GAAG,CAACN,KAAKS,YAAY,EAAE;4BAC5CJ,MAAM;4BACN,GAAGL,IAAI;wBACT;oBACF;oBACA;gBACF;YACA,KAAK;gBAAa;oBAChBb,mBAAmBM,GAAG,CAACa,GAAG,CAACH,MAAMM,YAAY,EAAEN;oBAC/C;gBACF;YACA;gBACEO,KAAIC,IAAI,CAAC,CAAC,SAAS,EAAET,SAAS,EAAE,EAAEC,MAAME,IAAI,CAAC,CAAC,CAAC;gBAC/C;QACJ;IACF;IAEA,MAAM,EAAEO,UAAU,EAAEb,eAAe,EAAE,GAAGb;IAExC,8DAA8D;IAC9D,8DAA8D;IAC9D,sCAAsC;IACtC,IAAIC,mBAAmBK,MAAM,CAACoB,UAAU,IAAI,CAACA,YAAY;QACvD,MAAMC,MAAMC,IAAAA,qBAAW,EAAC,QAAQ,UAAU;QAC1C,wCAAwC;QACxC1B,mBAAmB2B,MAAM,CAACF;IAC5B;IAEA1B,mBAAmBK,MAAM,CAACoB,UAAU,GAAGA;IAEvC,IAAIb,iBAAiB;QACnB,MAAMiB,yBAAyB,OAC7BC,MACAC;YAEA,MAAML,MAAMC,IAAAA,qBAAW,EAAC,QAAQ,UAAUG;YAE1C,MAAME,kBAAkB,MAAMpB,eAAe,CAACmB,KAAK,CAACE,WAAW;YAC/DC,IAAAA,oBAAa,EAACjC,oBAAoByB,KAAKM,iBAAiB,OAAO5B;QACjE;QACA,MAAMyB,uBAAuB,0BAA0B;QACvD,MAAMA,uBAAuB,wBAAwB;QACrD,MAAM3B,eAAeiC,sBAAsB,CACzC,mBACA;QAEF,MAAMjC,eAAekC,cAAc,CAAC;YAClCC,aAAaC;YACbnC;YACAJ,aAAaC;QACf;IACF;IAEA,IAAIyB,YAAY;QACd,MAAMC,MAAMC,IAAAA,qBAAW,EAAC,QAAQ,UAAU;QAE1C,MAAMY,WAAWd,WAAWc,QAAQ;QAEpC,eAAeC;YACb,MAAMR,kBAAkB,MAAMO,SAASN,WAAW;YAClDC,IAAAA,oBAAa,EAACjC,oBAAoByB,KAAKM,iBAAiB,OAAO5B;YAC/D,MAAMF,eAAeiC,sBAAsB,CAAC,cAAc;QAC5D;QACA,MAAMK;IACR,OAAO;QACLtC,eAAeuC,wBAAwB,CACrCd,IAAAA,qBAAW,EAAC,QAAQ,UAAU;IAElC;AACF;AAEO,eAAe9B,sBAAsB,EAC1CI,kBAAkB,EAClBF,WAAW,EACXG,cAAc,EACdC,kBAAkB,EAClBC,SAAS,EAOV;IACC,IAAIL,YAAYM,MAAM,CAACC,GAAG,EAAE;QAC1B,MAAMoB,MAAMC,IAAAA,qBAAW,EAAC,SAAS,UAAU;QAC3C,MAAMK,kBAAkB,MAAMjC,YAAYM,MAAM,CAACC,GAAG,CAAC2B,WAAW;QAChEC,IAAAA,oBAAa,EAACjC,oBAAoByB,KAAKM,iBAAiB,OAAO5B;IACjE;IACA,MAAMF,eAAewC,iBAAiB,CAAC;IACvC,MAAMxC,eAAeyC,iBAAiB,CAAC;IACvC,MAAMzC,eAAe0C,gBAAgB,CAAC;IAEtC,IAAI7C,YAAYM,MAAM,CAACG,QAAQ,EAAE;QAC/B,MAAMkB,MAAMC,IAAAA,qBAAW,EAAC,SAAS,UAAU;QAC3C,MAAMK,kBAAkB,MAAMjC,YAAYM,MAAM,CAACG,QAAQ,CAACyB,WAAW;QACrEC,IAAAA,oBAAa,EAACjC,oBAAoByB,KAAKM,iBAAiB,OAAO5B;IACjE;IACA,MAAMF,eAAeyC,iBAAiB,CAAC;IAEvC,IAAI5C,YAAYM,MAAM,CAACK,KAAK,EAAE;QAC5B,MAAMgB,MAAMC,IAAAA,qBAAW,EAAC,SAAS,UAAU;QAC3C,MAAMK,kBAAkB,MAAMjC,YAAYM,MAAM,CAACK,KAAK,CAACuB,WAAW;QAClEC,IAAAA,oBAAa,EAACjC,oBAAoByB,KAAKM,iBAAiB,OAAO5B;IACjE;IAEA,MAAMF,eAAewC,iBAAiB,CAAC;IACvC,MAAMxC,eAAeyC,iBAAiB,CAAC;IACvC,MAAMzC,eAAe0C,gBAAgB,CAAC;IAEtC,MAAM1C,eAAekC,cAAc,CAAC;QAClCC,aAAaC;QACbnC;QACAJ;IACF;AACF;AAEO,eAAeD,gBAAgB,EACpCe,IAAI,EACJG,KAAK,EACLf,kBAAkB,EAClBF,WAAW,EACXG,cAAc,EACdC,kBAAkB,EAClBC,SAAS,EAUV;IACC,MAAMyC,2BAA2BC,QAAQC,GAAG,CAACC,eAAe,IAAI;IAEhE,OAAQhC,MAAME,IAAI;QAChB,KAAK;YAAQ;gBACX,MAAM+B,YAAYtB,IAAAA,qBAAW,EAAC,SAAS,UAAUd;gBAEjD,IAAId,YAAYM,MAAM,CAACC,GAAG,EAAE;oBAC1B,MAAMoB,MAAMC,IAAAA,qBAAW,EAAC,SAAS,UAAU;oBAE3C,MAAMK,kBAAkB,MAAMjC,YAAYM,MAAM,CAACC,GAAG,CAAC2B,WAAW;oBAChEC,IAAAA,oBAAa,EACXjC,oBACAyB,KACAM,iBACA,OACA5B;gBAEJ;gBACA,MAAMF,eAAewC,iBAAiB,CAAC;gBACvC,MAAMxC,eAAeyC,iBAAiB,CAAC;gBAEvC,IAAI5C,YAAYM,MAAM,CAACG,QAAQ,EAAE;oBAC/B,MAAMkB,MAAMC,IAAAA,qBAAW,EAAC,SAAS,UAAU;oBAE3C,MAAMK,kBAAkB,MAAMjC,YAAYM,MAAM,CAACG,QAAQ,CAACyB,WAAW;oBACrEC,IAAAA,oBAAa,EACXjC,oBACAyB,KACAM,iBACA,OACA5B;gBAEJ;gBACA,MAAMF,eAAeyC,iBAAiB,CAAC;gBAEvC,MAAMX,kBAAkB,MAAMhB,MAAMkC,YAAY,CAACjB,WAAW;gBAE5D,MAAMf,OAAOc,mCAAAA,gBAAiBd,IAAI;gBAElC,MAAMhB,eAAewC,iBAAiB,CAAC7B;gBACvC,MAAMX,eAAeyC,iBAAiB,CAAC9B;gBACvC,IAAIK,SAAS,QAAQ;oBACnB,MAAMhB,eAAeiC,sBAAsB,CAACtB,MAAM;gBACpD,OAAO;oBACLX,eAAeuC,wBAAwB,CAACQ;gBAC1C;gBACA,MAAM/C,eAAe0C,gBAAgB,CAAC,SAAS;gBAC/C,MAAM1C,eAAe0C,gBAAgB,CAAC/B,MAAM;gBAE5C,IAAIgC,0BAA0B;oBAC5B,MAAM3C,eAAeiD,gBAAgB,CAACtC,MAAM;gBAC9C;gBAEA,MAAMX,eAAekC,cAAc,CAAC;oBAClCC,aAAaC;oBACbnC;oBACAJ;gBACF;gBAEAmC,IAAAA,oBAAa,EACXjC,oBACAgD,WACAjB,iBACA,OACA5B;gBAGF;YACF;QACA,KAAK;YAAY;gBACf,MAAMsB,MAAMC,IAAAA,qBAAW,EAAC,SAAS,UAAUd;gBAE3C,MAAMmB,kBAAkB,MAAMhB,MAAMuB,QAAQ,CAACN,WAAW;gBAExD,MAAMf,OAAOc,gBAAgBd,IAAI;gBAEjC,MAAMhB,eAAeyC,iBAAiB,CAAC9B;gBACvC,IAAIK,SAAS,QAAQ;oBACnB,MAAMhB,eAAeiC,sBAAsB,CAACtB,MAAM;gBACpD,OAAO;oBACLX,eAAeuC,wBAAwB,CAACf;gBAC1C;gBAEA,MAAMxB,eAAekC,cAAc,CAAC;oBAClCC,aAAaC;oBACbnC;oBACAJ;gBACF;gBAEAmC,IAAAA,oBAAa,EAACjC,oBAAoByB,KAAKM,iBAAiB,MAAM5B;gBAE9D;YACF;QACA,KAAK;YAAY;gBACf,MAAMsB,MAAMC,IAAAA,qBAAW,EAAC,OAAO,UAAUd;gBACzC,MAAMmB,kBAAkB,MAAMhB,MAAMkC,YAAY,CAACjB,WAAW;gBAC5D,MAAMf,OAAOc,gBAAgBd,IAAI;gBAEjC,IAAIA,SAAS,QAAQ;oBACnB,MAAMhB,eAAeiC,sBAAsB,CAACtB,MAAM;gBACpD,OAAO;oBACLX,eAAeuC,wBAAwB,CAACf;gBAC1C;gBAEA,MAAMxB,eAAekD,oBAAoB,CAACvC;gBAC1C,MAAMX,eAAewC,iBAAiB,CAAC7B,MAAM;gBAC7C,MAAMX,eAAemD,oBAAoB,CAACxC;gBAC1C,MAAMX,eAAeoD,kBAAkB,CAACzC;gBACxC,MAAMX,eAAe0C,gBAAgB,CAAC/B,MAAM;gBAE5C,IAAIgC,0BAA0B;oBAC5B,MAAM3C,eAAeiD,gBAAgB,CAACtC,MAAM;gBAC9C;gBAEA,MAAMX,eAAekC,cAAc,CAAC;oBAClCC,aAAaC;oBACbnC;oBACAJ;gBACF;gBAEAmC,IAAAA,oBAAa,EAACjC,oBAAoByB,KAAKM,iBAAiB,OAAO5B;gBAE/D;YACF;QACA,KAAK;YAAa;gBAChB,MAAMsB,MAAMC,IAAAA,qBAAW,EAAC,OAAO,UAAUd;gBACzC,MAAMmB,kBAAkB,MAAMhB,MAAMuB,QAAQ,CAACN,WAAW;gBACxD,MAAMf,OAAOc,gBAAgBd,IAAI;gBAEjC,MAAMhB,eAAemD,oBAAoB,CAACxC;gBAE1C,IAAIK,SAAS,QAAQ;oBACnB,MAAMhB,eAAeiC,sBAAsB,CAACtB,MAAM;gBACpD,OAAO;oBACLX,eAAeuC,wBAAwB,CAACf;gBAC1C;gBAEA,MAAMxB,eAAekC,cAAc,CAAC;oBAClCC,aAAaC;oBACbnC;oBACAJ;gBACF;gBACAmC,IAAAA,oBAAa,EAACjC,oBAAoByB,KAAKM,iBAAiB,MAAM5B;gBAE9D;YACF;QACA;YAAS;gBACP,MAAM,qBAAkE,CAAlE,IAAImD,MAAM,CAAC,mBAAmB,EAAE,AAACvC,MAAcE,IAAI,CAAC,KAAK,EAAEL,MAAM,GAAjE,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiE;YACzE;IACF;AACF"}