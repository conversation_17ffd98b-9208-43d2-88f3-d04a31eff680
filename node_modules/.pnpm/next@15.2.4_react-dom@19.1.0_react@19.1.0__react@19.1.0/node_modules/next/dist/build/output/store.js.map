{"version": 3, "sources": ["../../../src/build/output/store.ts"], "sourcesContent": ["import createStore from 'next/dist/compiled/unistore'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport { type Span, flushAllTraces, trace } from '../../trace'\nimport { teardownTraceSubscriber } from '../swc'\nimport * as Log from './log'\n\nconst MAX_LOG_SKIP_DURATION = 500 // 500ms\n\nexport type OutputState =\n  | {\n      bootstrap: true\n      appUrl: string | null\n      bindAddr: string | null\n      logging: boolean\n    }\n  | ({\n      bootstrap: false\n      appUrl: string | null\n      bindAddr: string | null\n      logging: boolean\n    } & (\n      | {\n          loading: true\n          trigger: string | undefined\n          url: string | undefined\n        }\n      | {\n          loading: false\n          typeChecking: boolean\n          totalModulesCount: number\n          errors: string[] | null\n          warnings: string[] | null\n          hasEdgeServer: boolean\n        }\n    ))\n\nexport function formatTrigger(trigger: string) {\n  // Format dynamic sitemap routes to simpler file path\n  // e.g., /sitemap.xml[] -> /sitemap.xml\n  if (trigger.includes('[__metadata_id__]')) {\n    trigger = trigger.replace('/[__metadata_id__]', '/[id]')\n  }\n\n  if (trigger.length > 1 && trigger.endsWith('/')) {\n    trigger = trigger.slice(0, -1)\n  }\n  return trigger\n}\n\nexport const store = createStore<OutputState>({\n  appUrl: null,\n  bindAddr: null,\n  bootstrap: true,\n  logging: true,\n})\n\nlet lastStore: OutputState = {\n  appUrl: null,\n  bindAddr: null,\n  bootstrap: true,\n  logging: true,\n}\nfunction hasStoreChanged(nextStore: OutputState) {\n  if (\n    (\n      [\n        ...new Set([...Object.keys(lastStore), ...Object.keys(nextStore)]),\n      ] as Array<keyof OutputState>\n    ).every((key) => Object.is(lastStore[key], nextStore[key]))\n  ) {\n    return false\n  }\n\n  lastStore = nextStore\n  return true\n}\n\nlet startTime = 0\nlet trigger = '' // default, use empty string for trigger\nlet triggerUrl: string | undefined = undefined\nlet loadingLogTimer: NodeJS.Timeout | null = null\nlet traceSpan: Span | null = null\nlet logging = true\n\nstore.subscribe((state) => {\n  // Update persisted logging state\n  if ('logging' in state) {\n    logging = state.logging\n  }\n\n  // If logging is disabled, do not log\n  if (!logging) {\n    return\n  }\n\n  if (!hasStoreChanged(state)) {\n    return\n  }\n\n  if (state.bootstrap) {\n    return\n  }\n\n  if (state.loading) {\n    if (state.trigger) {\n      trigger = formatTrigger(state.trigger)\n      triggerUrl = state.url\n      if (trigger !== 'initial') {\n        traceSpan = trace('compile-path', undefined, {\n          trigger: trigger,\n        })\n        if (!loadingLogTimer) {\n          // Only log compiling if compiled is not finished in 3 seconds\n          loadingLogTimer = setTimeout(() => {\n            if (\n              triggerUrl &&\n              triggerUrl !== trigger &&\n              process.env.NEXT_TRIGGER_URL\n            ) {\n              Log.wait(`Compiling ${trigger} (${triggerUrl}) ...`)\n            } else {\n              Log.wait(`Compiling ${trigger} ...`)\n            }\n          }, MAX_LOG_SKIP_DURATION)\n        }\n      }\n    }\n    if (startTime === 0) {\n      startTime = Date.now()\n    }\n    return\n  }\n\n  if (state.errors) {\n    // Log compilation errors\n    Log.error(state.errors[0])\n\n    const cleanError = stripAnsi(state.errors[0])\n    if (cleanError.indexOf('SyntaxError') > -1) {\n      const matches = cleanError.match(/\\[.*\\]=/)\n      if (matches) {\n        for (const match of matches) {\n          const prop = (match.split(']').shift() || '').slice(1)\n          console.log(\n            `AMP bind syntax [${prop}]='' is not supported in JSX, use 'data-amp-bind-${prop}' instead. https://nextjs.org/docs/messages/amp-bind-jsx-alt`\n          )\n        }\n        return\n      }\n    }\n    startTime = 0\n    // Ensure traces are flushed after each compile in development mode\n    flushAllTraces()\n    teardownTraceSubscriber()\n    return\n  }\n\n  let timeMessage = ''\n  if (startTime) {\n    const time = Date.now() - startTime\n    startTime = 0\n\n    timeMessage =\n      ' ' +\n      (time > 2000 ? `in ${Math.round(time / 100) / 10}s` : `in ${time}ms`)\n  }\n\n  let modulesMessage = ''\n  if (state.totalModulesCount) {\n    modulesMessage = ` (${state.totalModulesCount} modules)`\n  }\n\n  if (state.warnings) {\n    Log.warn(state.warnings.join('\\n\\n'))\n    // Ensure traces are flushed after each compile in development mode\n    flushAllTraces()\n    teardownTraceSubscriber()\n    return\n  }\n\n  if (state.typeChecking) {\n    Log.info(\n      `bundled ${trigger}${timeMessage}${modulesMessage}, type checking...`\n    )\n    return\n  }\n\n  if (trigger === 'initial') {\n    trigger = ''\n  } else {\n    if (loadingLogTimer) {\n      clearTimeout(loadingLogTimer)\n      loadingLogTimer = null\n    }\n    if (traceSpan) {\n      traceSpan.stop()\n      traceSpan = null\n    }\n    Log.event(\n      `Compiled${trigger ? ' ' + trigger : ''}${timeMessage}${modulesMessage}`\n    )\n    trigger = ''\n  }\n\n  // Ensure traces are flushed after each compile in development mode\n  flushAllTraces()\n  teardownTraceSubscriber()\n})\n"], "names": ["formatTrigger", "store", "MAX_LOG_SKIP_DURATION", "trigger", "includes", "replace", "length", "endsWith", "slice", "createStore", "appUrl", "bindAddr", "bootstrap", "logging", "lastStore", "hasStoreChanged", "nextStore", "Set", "Object", "keys", "every", "key", "is", "startTime", "triggerUrl", "undefined", "loadingLogTimer", "traceSpan", "subscribe", "state", "loading", "url", "trace", "setTimeout", "process", "env", "NEXT_TRIGGER_URL", "Log", "wait", "Date", "now", "errors", "error", "cleanError", "stripAnsi", "indexOf", "matches", "match", "prop", "split", "shift", "console", "log", "flushAllTraces", "teardownTraceSubscriber", "timeMessage", "time", "Math", "round", "modulesMessage", "totalModulesCount", "warnings", "warn", "join", "typeChecking", "info", "clearTimeout", "stop", "event"], "mappings": ";;;;;;;;;;;;;;;IAoCgBA,aAAa;eAAbA;;IAaHC,KAAK;eAALA;;;iEAjDW;kEACF;uBAC2B;qBACT;6DACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMC,wBAAwB,IAAI,QAAQ;;AA8BnC,SAASF,cAAcG,OAAe;IAC3C,qDAAqD;IACrD,uCAAuC;IACvC,IAAIA,QAAQC,QAAQ,CAAC,sBAAsB;QACzCD,UAAUA,QAAQE,OAAO,CAAC,sBAAsB;IAClD;IAEA,IAAIF,QAAQG,MAAM,GAAG,KAAKH,QAAQI,QAAQ,CAAC,MAAM;QAC/CJ,UAAUA,QAAQK,KAAK,CAAC,GAAG,CAAC;IAC9B;IACA,OAAOL;AACT;AAEO,MAAMF,QAAQQ,IAAAA,iBAAW,EAAc;IAC5CC,QAAQ;IACRC,UAAU;IACVC,WAAW;IACXC,SAAS;AACX;AAEA,IAAIC,YAAyB;IAC3BJ,QAAQ;IACRC,UAAU;IACVC,WAAW;IACXC,SAAS;AACX;AACA,SAASE,gBAAgBC,SAAsB;IAC7C,IACE,AACE;WACK,IAAIC,IAAI;eAAIC,OAAOC,IAAI,CAACL;eAAeI,OAAOC,IAAI,CAACH;SAAW;KAClE,CACDI,KAAK,CAAC,CAACC,MAAQH,OAAOI,EAAE,CAACR,SAAS,CAACO,IAAI,EAAEL,SAAS,CAACK,IAAI,IACzD;QACA,OAAO;IACT;IAEAP,YAAYE;IACZ,OAAO;AACT;AAEA,IAAIO,YAAY;AAChB,IAAIpB,UAAU,GAAG,wCAAwC;;AACzD,IAAIqB,aAAiCC;AACrC,IAAIC,kBAAyC;AAC7C,IAAIC,YAAyB;AAC7B,IAAId,UAAU;AAEdZ,MAAM2B,SAAS,CAAC,CAACC;IACf,iCAAiC;IACjC,IAAI,aAAaA,OAAO;QACtBhB,UAAUgB,MAAMhB,OAAO;IACzB;IAEA,qCAAqC;IACrC,IAAI,CAACA,SAAS;QACZ;IACF;IAEA,IAAI,CAACE,gBAAgBc,QAAQ;QAC3B;IACF;IAEA,IAAIA,MAAMjB,SAAS,EAAE;QACnB;IACF;IAEA,IAAIiB,MAAMC,OAAO,EAAE;QACjB,IAAID,MAAM1B,OAAO,EAAE;YACjBA,UAAUH,cAAc6B,MAAM1B,OAAO;YACrCqB,aAAaK,MAAME,GAAG;YACtB,IAAI5B,YAAY,WAAW;gBACzBwB,YAAYK,IAAAA,YAAK,EAAC,gBAAgBP,WAAW;oBAC3CtB,SAASA;gBACX;gBACA,IAAI,CAACuB,iBAAiB;oBACpB,8DAA8D;oBAC9DA,kBAAkBO,WAAW;wBAC3B,IACET,cACAA,eAAerB,WACf+B,QAAQC,GAAG,CAACC,gBAAgB,EAC5B;4BACAC,KAAIC,IAAI,CAAC,CAAC,UAAU,EAAEnC,QAAQ,EAAE,EAAEqB,WAAW,KAAK,CAAC;wBACrD,OAAO;4BACLa,KAAIC,IAAI,CAAC,CAAC,UAAU,EAAEnC,QAAQ,IAAI,CAAC;wBACrC;oBACF,GAAGD;gBACL;YACF;QACF;QACA,IAAIqB,cAAc,GAAG;YACnBA,YAAYgB,KAAKC,GAAG;QACtB;QACA;IACF;IAEA,IAAIX,MAAMY,MAAM,EAAE;QAChB,yBAAyB;QACzBJ,KAAIK,KAAK,CAACb,MAAMY,MAAM,CAAC,EAAE;QAEzB,MAAME,aAAaC,IAAAA,kBAAS,EAACf,MAAMY,MAAM,CAAC,EAAE;QAC5C,IAAIE,WAAWE,OAAO,CAAC,iBAAiB,CAAC,GAAG;YAC1C,MAAMC,UAAUH,WAAWI,KAAK,CAAC;YACjC,IAAID,SAAS;gBACX,KAAK,MAAMC,SAASD,QAAS;oBAC3B,MAAME,OAAO,AAACD,CAAAA,MAAME,KAAK,CAAC,KAAKC,KAAK,MAAM,EAAC,EAAG1C,KAAK,CAAC;oBACpD2C,QAAQC,GAAG,CACT,CAAC,iBAAiB,EAAEJ,KAAK,iDAAiD,EAAEA,KAAK,4DAA4D,CAAC;gBAElJ;gBACA;YACF;QACF;QACAzB,YAAY;QACZ,mEAAmE;QACnE8B,IAAAA,qBAAc;QACdC,IAAAA,4BAAuB;QACvB;IACF;IAEA,IAAIC,cAAc;IAClB,IAAIhC,WAAW;QACb,MAAMiC,OAAOjB,KAAKC,GAAG,KAAKjB;QAC1BA,YAAY;QAEZgC,cACE,MACCC,CAAAA,OAAO,OAAO,CAAC,GAAG,EAAEC,KAAKC,KAAK,CAACF,OAAO,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAEA,KAAK,EAAE,CAAC,AAAD;IACvE;IAEA,IAAIG,iBAAiB;IACrB,IAAI9B,MAAM+B,iBAAiB,EAAE;QAC3BD,iBAAiB,CAAC,EAAE,EAAE9B,MAAM+B,iBAAiB,CAAC,SAAS,CAAC;IAC1D;IAEA,IAAI/B,MAAMgC,QAAQ,EAAE;QAClBxB,KAAIyB,IAAI,CAACjC,MAAMgC,QAAQ,CAACE,IAAI,CAAC;QAC7B,mEAAmE;QACnEV,IAAAA,qBAAc;QACdC,IAAAA,4BAAuB;QACvB;IACF;IAEA,IAAIzB,MAAMmC,YAAY,EAAE;QACtB3B,KAAI4B,IAAI,CACN,CAAC,QAAQ,EAAE9D,UAAUoD,cAAcI,eAAe,kBAAkB,CAAC;QAEvE;IACF;IAEA,IAAIxD,YAAY,WAAW;QACzBA,UAAU;IACZ,OAAO;QACL,IAAIuB,iBAAiB;YACnBwC,aAAaxC;YACbA,kBAAkB;QACpB;QACA,IAAIC,WAAW;YACbA,UAAUwC,IAAI;YACdxC,YAAY;QACd;QACAU,KAAI+B,KAAK,CACP,CAAC,QAAQ,EAAEjE,UAAU,MAAMA,UAAU,KAAKoD,cAAcI,gBAAgB;QAE1ExD,UAAU;IACZ;IAEA,mEAAmE;IACnEkD,IAAAA,qBAAc;IACdC,IAAAA,4BAAuB;AACzB"}