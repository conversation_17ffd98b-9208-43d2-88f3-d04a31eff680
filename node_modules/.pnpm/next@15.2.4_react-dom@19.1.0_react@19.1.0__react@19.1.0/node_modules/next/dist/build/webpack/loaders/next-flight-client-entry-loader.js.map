{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-flight-client-entry-loader.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport {\n  BARREL_OPTIMIZATION_PREFIX,\n  RSC_MODULE_TYPES,\n} from '../../../shared/lib/constants'\nimport { getModuleBuildInfo } from './get-module-build-info'\nimport { regexCSS } from './utils'\n\n/**\n * { [client import path]: [exported names] }\n */\nexport type ClientComponentImports = Record<string, Set<string>>\nexport type CssImports = Record<string, string[]>\n\nexport type NextFlightClientEntryLoaderOptions = {\n  modules: string[] | string\n  /** This is transmitted as a string to `getOptions` */\n  server: boolean | 'true' | 'false'\n}\n\nexport type FlightClientEntryModuleItem = {\n  // module path\n  request: string\n  // imported identifiers\n  ids: string[]\n}\n\nexport default function transformSource(\n  this: webpack.LoaderContext<NextFlightClientEntryLoaderOptions>\n) {\n  let { modules, server } = this.getOptions()\n  const isServer = server === 'true'\n\n  if (!Array.isArray(modules)) {\n    modules = modules ? [modules] : []\n  }\n\n  const code = modules\n    .map((x) => JSON.parse(x) as FlightClientEntryModuleItem)\n    // Filter out CSS files in the SSR compilation\n    .filter(({ request }) => (isServer ? !regexCSS.test(request) : true))\n    .map(({ request, ids }: FlightClientEntryModuleItem) => {\n      const importPath = JSON.stringify(\n        request.startsWith(BARREL_OPTIMIZATION_PREFIX)\n          ? request.replace(':', '!=!')\n          : request\n      )\n\n      // When we cannot determine the export names, we use eager mode to include the whole module.\n      // Otherwise, we use eager mode with webpackExports to only include the necessary exports.\n      // If we have '*' in the ids, we include all the imports\n      if (ids.length === 0 || ids.includes('*')) {\n        return `import(/* webpackMode: \"eager\" */ ${importPath});\\n`\n      } else {\n        return `import(/* webpackMode: \"eager\", webpackExports: ${JSON.stringify(\n          ids\n        )} */ ${importPath});\\n`\n      }\n    })\n    .join(';\\n')\n\n  const buildInfo = getModuleBuildInfo(this._module!)\n\n  buildInfo.rsc = {\n    type: RSC_MODULE_TYPES.client,\n  }\n  if (process.env.BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN) {\n    const rscModuleInformationJson = JSON.stringify(buildInfo.rsc)\n    return (\n      `/* __rspack_internal_rsc_module_information_do_not_use__ ${rscModuleInformationJson} */\\n` +\n      code\n    )\n  }\n\n  return code\n}\n"], "names": ["transformSource", "modules", "server", "getOptions", "isServer", "Array", "isArray", "code", "map", "x", "JSON", "parse", "filter", "request", "regexCSS", "test", "ids", "importPath", "stringify", "startsWith", "BARREL_OPTIMIZATION_PREFIX", "replace", "length", "includes", "join", "buildInfo", "getModuleBuildInfo", "_module", "rsc", "type", "RSC_MODULE_TYPES", "client", "process", "env", "BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN", "rscModuleInformationJson"], "mappings": ";;;;+BA2BA;;;eAAwBA;;;2BAvBjB;oCAC4B;uBACV;AAqBV,SAASA;IAGtB,IAAI,EAAEC,OAAO,EAAEC,MAAM,EAAE,GAAG,IAAI,CAACC,UAAU;IACzC,MAAMC,WAAWF,WAAW;IAE5B,IAAI,CAACG,MAAMC,OAAO,CAACL,UAAU;QAC3BA,UAAUA,UAAU;YAACA;SAAQ,GAAG,EAAE;IACpC;IAEA,MAAMM,OAAON,QACVO,GAAG,CAAC,CAACC,IAAMC,KAAKC,KAAK,CAACF,GACvB,8CAA8C;KAC7CG,MAAM,CAAC,CAAC,EAAEC,OAAO,EAAE,GAAMT,WAAW,CAACU,eAAQ,CAACC,IAAI,CAACF,WAAW,MAC9DL,GAAG,CAAC,CAAC,EAAEK,OAAO,EAAEG,GAAG,EAA+B;QACjD,MAAMC,aAAaP,KAAKQ,SAAS,CAC/BL,QAAQM,UAAU,CAACC,qCAA0B,IACzCP,QAAQQ,OAAO,CAAC,KAAK,SACrBR;QAGN,4FAA4F;QAC5F,0FAA0F;QAC1F,wDAAwD;QACxD,IAAIG,IAAIM,MAAM,KAAK,KAAKN,IAAIO,QAAQ,CAAC,MAAM;YACzC,OAAO,CAAC,kCAAkC,EAAEN,WAAW,IAAI,CAAC;QAC9D,OAAO;YACL,OAAO,CAAC,gDAAgD,EAAEP,KAAKQ,SAAS,CACtEF,KACA,IAAI,EAAEC,WAAW,IAAI,CAAC;QAC1B;IACF,GACCO,IAAI,CAAC;IAER,MAAMC,YAAYC,IAAAA,sCAAkB,EAAC,IAAI,CAACC,OAAO;IAEjDF,UAAUG,GAAG,GAAG;QACdC,MAAMC,2BAAgB,CAACC,MAAM;IAC/B;IACA,IAAIC,QAAQC,GAAG,CAACC,kCAAkC,EAAE;QAClD,MAAMC,2BAA2BzB,KAAKQ,SAAS,CAACO,UAAUG,GAAG;QAC7D,OACE,CAAC,yDAAyD,EAAEO,yBAAyB,KAAK,CAAC,GAC3F5B;IAEJ;IAEA,OAAOA;AACT"}