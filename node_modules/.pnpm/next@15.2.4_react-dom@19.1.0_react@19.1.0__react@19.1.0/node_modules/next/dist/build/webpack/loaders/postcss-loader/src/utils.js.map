{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/postcss-loader/src/utils.ts"], "sourcesContent": ["import path from 'path'\n\nconst IS_NATIVE_WIN32_PATH = /^[a-z]:[/\\\\]|^\\\\\\\\/i\nconst ABSOLUTE_SCHEME = /^[a-z0-9+\\-.]+:/i\n\nfunction getURLType(source: string) {\n  if (source[0] === '/') {\n    if (source[1] === '/') {\n      return 'scheme-relative'\n    }\n\n    return 'path-absolute'\n  }\n\n  if (IS_NATIVE_WIN32_PATH.test(source)) {\n    return 'path-absolute'\n  }\n\n  return ABSOLUTE_SCHEME.test(source) ? 'absolute' : 'path-relative'\n}\n\nfunction normalizeSourceMap(map: any, resourceContext: string) {\n  let newMap = map\n\n  // Some loader emit source map as string\n  // Strip any JSON XSSI avoidance prefix from the string (as documented in the source maps specification), and then parse the string as JSON.\n  if (typeof newMap === 'string') {\n    newMap = JSON.parse(newMap)\n  }\n\n  delete newMap.file\n\n  const { sourceRoot } = newMap\n\n  delete newMap.sourceRoot\n\n  if (newMap.sources) {\n    newMap.sources = newMap.sources.map((source: string) => {\n      const sourceType = getURLType(source)\n\n      // Do no touch `scheme-relative` and `absolute` URLs\n      if (sourceType === 'path-relative' || sourceType === 'path-absolute') {\n        const absoluteSource =\n          sourceType === 'path-relative' && sourceRoot\n            ? path.resolve(sourceRoot, path.normalize(source))\n            : path.normalize(source)\n\n        return path.relative(resourceContext, absoluteSource)\n      }\n\n      return source\n    })\n  }\n\n  return newMap\n}\n\nfunction normalizeSourceMapAfterPostcss(map: any, resourceContext: string) {\n  const newMap = map\n\n  // result.map.file is an optional property that provides the output filename.\n  // Since we don't know the final filename in the webpack build chain yet, it makes no sense to have it.\n  // eslint-disable-next-line no-param-reassign\n  delete newMap.file\n\n  // eslint-disable-next-line no-param-reassign\n  newMap.sourceRoot = ''\n\n  // eslint-disable-next-line no-param-reassign\n  newMap.sources = newMap.sources.map((source: string) => {\n    if (source.startsWith('<')) {\n      return source\n    }\n\n    const sourceType = getURLType(source)\n\n    // Do no touch `scheme-relative`, `path-absolute` and `absolute` types\n    if (sourceType === 'path-relative') {\n      return path.resolve(resourceContext, source)\n    }\n\n    return source\n  })\n\n  return newMap\n}\n\nexport { normalizeSourceMap, normalizeSourceMapAfterPostcss }\n"], "names": ["normalizeSourceMap", "normalizeSourceMapAfterPostcss", "IS_NATIVE_WIN32_PATH", "ABSOLUTE_SCHEME", "getURLType", "source", "test", "map", "resourceContext", "newMap", "JSON", "parse", "file", "sourceRoot", "sources", "sourceType", "absoluteSource", "path", "resolve", "normalize", "relative", "startsWith"], "mappings": ";;;;;;;;;;;;;;;IAuFSA,kBAAkB;eAAlBA;;IAAoBC,8BAA8B;eAA9BA;;;6DAvFZ;;;;;;AAEjB,MAAMC,uBAAuB;AAC7B,MAAMC,kBAAkB;AAExB,SAASC,WAAWC,MAAc;IAChC,IAAIA,MAAM,CAAC,EAAE,KAAK,KAAK;QACrB,IAAIA,MAAM,CAAC,EAAE,KAAK,KAAK;YACrB,OAAO;QACT;QAEA,OAAO;IACT;IAEA,IAAIH,qBAAqBI,IAAI,CAACD,SAAS;QACrC,OAAO;IACT;IAEA,OAAOF,gBAAgBG,IAAI,CAACD,UAAU,aAAa;AACrD;AAEA,SAASL,mBAAmBO,GAAQ,EAAEC,eAAuB;IAC3D,IAAIC,SAASF;IAEb,wCAAwC;IACxC,4IAA4I;IAC5I,IAAI,OAAOE,WAAW,UAAU;QAC9BA,SAASC,KAAKC,KAAK,CAACF;IACtB;IAEA,OAAOA,OAAOG,IAAI;IAElB,MAAM,EAAEC,UAAU,EAAE,GAAGJ;IAEvB,OAAOA,OAAOI,UAAU;IAExB,IAAIJ,OAAOK,OAAO,EAAE;QAClBL,OAAOK,OAAO,GAAGL,OAAOK,OAAO,CAACP,GAAG,CAAC,CAACF;YACnC,MAAMU,aAAaX,WAAWC;YAE9B,oDAAoD;YACpD,IAAIU,eAAe,mBAAmBA,eAAe,iBAAiB;gBACpE,MAAMC,iBACJD,eAAe,mBAAmBF,aAC9BI,aAAI,CAACC,OAAO,CAACL,YAAYI,aAAI,CAACE,SAAS,CAACd,WACxCY,aAAI,CAACE,SAAS,CAACd;gBAErB,OAAOY,aAAI,CAACG,QAAQ,CAACZ,iBAAiBQ;YACxC;YAEA,OAAOX;QACT;IACF;IAEA,OAAOI;AACT;AAEA,SAASR,+BAA+BM,GAAQ,EAAEC,eAAuB;IACvE,MAAMC,SAASF;IAEf,6EAA6E;IAC7E,uGAAuG;IACvG,6CAA6C;IAC7C,OAAOE,OAAOG,IAAI;IAElB,6CAA6C;IAC7CH,OAAOI,UAAU,GAAG;IAEpB,6CAA6C;IAC7CJ,OAAOK,OAAO,GAAGL,OAAOK,OAAO,CAACP,GAAG,CAAC,CAACF;QACnC,IAAIA,OAAOgB,UAAU,CAAC,MAAM;YAC1B,OAAOhB;QACT;QAEA,MAAMU,aAAaX,WAAWC;QAE9B,sEAAsE;QACtE,IAAIU,eAAe,iBAAiB;YAClC,OAAOE,aAAI,CAACC,OAAO,CAACV,iBAAiBH;QACvC;QAEA,OAAOA;IACT;IAEA,OAAOI;AACT"}