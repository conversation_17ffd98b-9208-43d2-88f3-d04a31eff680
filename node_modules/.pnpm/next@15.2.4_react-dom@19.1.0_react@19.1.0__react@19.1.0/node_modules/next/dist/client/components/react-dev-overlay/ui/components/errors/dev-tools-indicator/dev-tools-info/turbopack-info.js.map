{"version": 3, "sources": ["../../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/turbopack-info.tsx"], "sourcesContent": ["import { DevToolsInfo, type DevToolsInfoPropsCore } from './dev-tools-info'\nimport { CopyButton } from '../../../copy-button'\nimport type { HTMLProps } from 'react'\n\nexport function TurbopackInfo(\n  props: DevToolsInfoPropsCore & HTMLProps<HTMLDivElement>\n) {\n  return (\n    <DevToolsInfo\n      title=\"Turbopack\"\n      learnMoreLink=\"https://nextjs.org/docs/app/api-reference/turbopack\"\n      {...props}\n    >\n      <article className=\"dev-tools-info-article\">\n        <p className=\"dev-tools-info-paragraph\">\n          Turbopack is an incremental bundler optimized for JavaScript and\n          TypeScript, written in Rust, and built into Next.js. Turbopack can be\n          used in Next.js in both the{' '}\n          <code className=\"dev-tools-info-code\">pages</code> and{' '}\n          <code className=\"dev-tools-info-code\">app</code> directories for\n          faster local development.\n        </p>\n        <p className=\"dev-tools-info-paragraph\">\n          To enable Turbopack, use the{' '}\n          <code className=\"dev-tools-info-code\">--turbopack</code> flag when\n          running the Next.js development server.\n        </p>\n      </article>\n\n      <div className=\"dev-tools-info-code-block-container\">\n        <div className=\"dev-tools-info-code-block\">\n          <CopyButton\n            actionLabel=\"Copy Next.js Turbopack Command\"\n            successLabel=\"Next.js Turbopack Command Copied\"\n            content={'--turbopack'}\n            className=\"dev-tools-info-copy-button\"\n          />\n          <pre className=\"dev-tools-info-code-block-pre\">\n            <code>\n              <div className=\"dev-tools-info-code-block-line\">{'  '}</div>\n              <div className=\"dev-tools-info-code-block-line\">{'{'}</div>\n              <div className=\"dev-tools-info-code-block-line\">\n                {'  '}\n                <span className=\"dev-tools-info-code-block-json-key\">\n                  \"scripts\"\n                </span>\n                : {'{'}\n              </div>\n              <div className=\"dev-tools-info-code-block-line dev-tools-info-highlight\">\n                {'    '}\n                <span className=\"dev-tools-info-code-block-json-key\">\n                  \"dev\"\n                </span>\n                :{' '}\n                <span className=\"dev-tools-info-code-block-json-value\">\n                  \"next dev --turbopack\"\n                </span>\n                ,\n              </div>\n              <div className=\"dev-tools-info-code-block-line\">\n                {'    '}\n                <span className=\"dev-tools-info-code-block-json-key\">\n                  \"build\"\n                </span>\n                :{' '}\n                <span className=\"dev-tools-info-code-block-json-value\">\n                  \"next build\"\n                </span>\n                ,\n              </div>\n              <div className=\"dev-tools-info-code-block-line\">\n                {'    '}\n                <span className=\"dev-tools-info-code-block-json-key\">\n                  \"start\"\n                </span>\n                :{' '}\n                <span className=\"dev-tools-info-code-block-json-value\">\n                  \"next start\"\n                </span>\n                ,\n              </div>\n              <div className=\"dev-tools-info-code-block-line\">\n                {'    '}\n                <span className=\"dev-tools-info-code-block-json-key\">\n                  \"lint\"\n                </span>\n                :{' '}\n                <span className=\"dev-tools-info-code-block-json-value\">\n                  \"next lint\"\n                </span>\n              </div>\n              <div className=\"dev-tools-info-code-block-line\">{'  }'}</div>\n              <div className=\"dev-tools-info-code-block-line\">{'}'}</div>\n              <div className=\"dev-tools-info-code-block-line\">{'  '}</div>\n            </code>\n          </pre>\n        </div>\n      </div>\n    </DevToolsInfo>\n  )\n}\n\nexport const DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES = `\n  .dev-tools-info-code {\n    background: var(--color-gray-400);\n    color: var(--color-gray-1000);\n    font-family: var(--font-stack-monospace);\n    padding: 2px 4px;\n    margin: 0;\n    font-size: var(--size-13);\n    white-space: break-spaces;\n    border-radius: var(--rounded-md-2);\n  }\n\n  .dev-tools-info-code-block-container {\n    padding: 6px;\n  }\n\n  .dev-tools-info-code-block {\n    position: relative;\n    background: var(--color-background-200);\n    border: 1px solid var(--color-gray-alpha-400);\n    border-radius: var(--rounded-md-2);\n    min-width: 326px;\n  }\n\n  .dev-tools-info-code-block-pre {\n    margin: 0;\n    font-family: var(--font-stack-monospace);\n    font-size: var(--size-12);\n  }\n\n  .dev-tools-info-copy-button {\n    position: absolute;\n\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    right: 8px;\n    top: 8px;\n    padding: 4px;\n    height: var(--size-24);\n    width: var(--size-24);\n    border-radius: var(--rounded-md-2);\n    border: 1px solid var(--color-gray-alpha-400);\n    background: var(--color-background-100);\n  }\n\n  .dev-tools-info-code-block-line {\n    display: block;\n    line-height: 1.5;\n    padding: 0 16px;\n  }\n\n  .dev-tools-info-code-block-line.dev-tools-info-highlight {\n    border-left: 2px solid var(--color-blue-900);\n    background: var(--color-blue-400);\n  }\n\n  .dev-tools-info-code-block-json-key {\n    color: var(--color-syntax-keyword);\n  }\n\n  .dev-tools-info-code-block-json-value {\n    color: var(--color-syntax-link);\n  }\n`\n"], "names": ["DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES", "TurbopackInfo", "props", "DevToolsInfo", "title", "learnMoreLink", "article", "className", "p", "code", "div", "Copy<PERSON><PERSON><PERSON>", "actionLabel", "successLabel", "content", "pre", "span"], "mappings": ";;;;;;;;;;;;;;;IAsGaA,oCAAoC;eAApCA;;IAlGGC,aAAa;eAAbA;;;;8BAJyC;4BAC9B;AAGpB,SAASA,cACdC,KAAwD;IAExD,qBACE,sBAACC,0BAAY;QACXC,OAAM;QACNC,eAAc;QACb,GAAGH,KAAK;;0BAET,sBAACI;gBAAQC,WAAU;;kCACjB,sBAACC;wBAAED,WAAU;;4BAA2B;4BAGV;0CAC5B,qBAACE;gCAAKF,WAAU;0CAAsB;;4BAAY;4BAAK;0CACvD,qBAACE;gCAAKF,WAAU;0CAAsB;;4BAAU;;;kCAGlD,sBAACC;wBAAED,WAAU;;4BAA2B;4BACT;0CAC7B,qBAACE;gCAAKF,WAAU;0CAAsB;;4BAAkB;;;;;0BAK5D,qBAACG;gBAAIH,WAAU;0BACb,cAAA,sBAACG;oBAAIH,WAAU;;sCACb,qBAACI,sBAAU;4BACTC,aAAY;4BACZC,cAAa;4BACbC,SAAS;4BACTP,WAAU;;sCAEZ,qBAACQ;4BAAIR,WAAU;sCACb,cAAA,sBAACE;;kDACC,qBAACC;wCAAIH,WAAU;kDAAkC;;kDACjD,qBAACG;wCAAIH,WAAU;kDAAkC;;kDACjD,sBAACG;wCAAIH,WAAU;;4CACZ;0DACD,qBAACS;gDAAKT,WAAU;0DAAqC;;4CAE9C;4CACJ;;;kDAEL,sBAACG;wCAAIH,WAAU;;4CACZ;0DACD,qBAACS;gDAAKT,WAAU;0DAAqC;;4CAE9C;4CACL;0DACF,qBAACS;gDAAKT,WAAU;0DAAuC;;4CAEhD;;;kDAGT,sBAACG;wCAAIH,WAAU;;4CACZ;0DACD,qBAACS;gDAAKT,WAAU;0DAAqC;;4CAE9C;4CACL;0DACF,qBAACS;gDAAKT,WAAU;0DAAuC;;4CAEhD;;;kDAGT,sBAACG;wCAAIH,WAAU;;4CACZ;0DACD,qBAACS;gDAAKT,WAAU;0DAAqC;;4CAE9C;4CACL;0DACF,qBAACS;gDAAKT,WAAU;0DAAuC;;4CAEhD;;;kDAGT,sBAACG;wCAAIH,WAAU;;4CACZ;0DACD,qBAACS;gDAAKT,WAAU;0DAAqC;;4CAE9C;4CACL;0DACF,qBAACS;gDAAKT,WAAU;0DAAuC;;;;kDAIzD,qBAACG;wCAAIH,WAAU;kDAAkC;;kDACjD,qBAACG;wCAAIH,WAAU;kDAAkC;;kDACjD,qBAACG;wCAAIH,WAAU;kDAAkC;;;;;;;;;;AAO/D;AAEO,MAAMP,uCAAwC"}