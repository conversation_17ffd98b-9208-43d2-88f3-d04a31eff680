{"version": 3, "sources": ["../../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.tsx"], "sourcesContent": ["import { useState, type HTMLProps } from 'react'\nimport { css } from '../../../../../utils/css'\nimport EyeIcon from '../../../../icons/eye-icon'\nimport { STORAGE_KEY_POSITION, STORAGE_KEY_THEME } from '../../../../../shared'\nimport LightIcon from '../../../../icons/light-icon'\nimport DarkIcon from '../../../../icons/dark-icon'\nimport SystemIcon from '../../../../icons/system-icon'\nimport type { DevToolsInfoPropsCore } from './dev-tools-info'\nimport { DevToolsInfo } from './dev-tools-info'\nimport type { DevToolsIndicatorPosition } from '../dev-tools-indicator'\n\nfunction getInitialPreference() {\n  if (typeof localStorage === 'undefined') {\n    return 'system'\n  }\n\n  const theme = localStorage.getItem(STORAGE_KEY_THEME)\n  return theme === 'dark' || theme === 'light' ? theme : 'system'\n}\n\nexport function UserPreferences({\n  setPosition,\n  position,\n  hide,\n  ...props\n}: {\n  setPosition: (position: DevToolsIndicatorPosition) => void\n  position: DevToolsIndicatorPosition\n  hide: () => void\n} & DevToolsInfoPropsCore &\n  HTMLProps<HTMLDivElement>) {\n  // derive initial theme from system preference\n  const [theme, setTheme] = useState(getInitialPreference())\n\n  const handleThemeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const portal = document.querySelector('nextjs-portal')\n    if (!portal) return\n\n    setTheme(e.target.value)\n\n    if (e.target.value === 'system') {\n      portal.classList.remove('dark')\n      portal.classList.remove('light')\n      localStorage.removeItem(STORAGE_KEY_THEME)\n      return\n    }\n\n    if (e.target.value === 'dark') {\n      portal.classList.add('dark')\n      portal.classList.remove('light')\n      localStorage.setItem(STORAGE_KEY_THEME, 'dark')\n    } else {\n      portal.classList.remove('dark')\n      portal.classList.add('light')\n      localStorage.setItem(STORAGE_KEY_THEME, 'light')\n    }\n  }\n\n  function handlePositionChange(e: React.ChangeEvent<HTMLSelectElement>) {\n    setPosition(e.target.value as DevToolsIndicatorPosition)\n    localStorage.setItem(STORAGE_KEY_POSITION, e.target.value)\n  }\n\n  return (\n    <DevToolsInfo title=\"Preferences\" {...props}>\n      <div className=\"preferences-container\">\n        <div className=\"preference-section\">\n          <div className=\"preference-header\">\n            <label htmlFor=\"theme\">Theme</label>\n            <p className=\"preference-description\">\n              Select your theme preference.\n            </p>\n          </div>\n          <div className=\"preference-control-select\">\n            <div className=\"preference-icon\">\n              <ThemeIcon theme={theme as 'dark' | 'light' | 'system'} />\n            </div>\n            <select\n              id=\"theme\"\n              name=\"theme\"\n              className=\"select-button\"\n              value={theme}\n              onChange={handleThemeChange}\n            >\n              <option value=\"system\">System</option>\n              <option value=\"light\">Light</option>\n              <option value=\"dark\">Dark</option>\n            </select>\n          </div>\n        </div>\n\n        <div className=\"preference-section\">\n          <div className=\"preference-header\">\n            <label htmlFor=\"position\">Position</label>\n            <p className=\"preference-description\">\n              Adjust the placement of your dev tools.\n            </p>\n          </div>\n          <div className=\"preference-control-select\">\n            <select\n              id=\"position\"\n              name=\"position\"\n              className=\"select-button\"\n              value={position}\n              onChange={handlePositionChange}\n            >\n              <option value=\"bottom-left\">Bottom Left</option>\n              <option value=\"bottom-right\">Bottom Right</option>\n              <option value=\"top-left\">Top Left</option>\n              <option value=\"top-right\">Top Right</option>\n            </select>\n          </div>\n        </div>\n\n        <div className=\"preference-section\">\n          <div className=\"preference-header\">\n            <label htmlFor=\"hide-dev-tools\">\n              Hide Dev Tools for this session\n            </label>\n            <p className=\"preference-description\">\n              Hide Dev Tools until you restart your dev server, or 1 day.\n            </p>\n          </div>\n          <div className=\"preference-control\">\n            <button\n              id=\"hide-dev-tools\"\n              name=\"hide-dev-tools\"\n              data-hide-dev-tools\n              className=\"action-button\"\n              onClick={hide}\n            >\n              <div className=\"preference-icon\">\n                <EyeIcon />\n              </div>\n              <span>Hide</span>\n            </button>\n          </div>\n        </div>\n\n        <div className=\"preference-section\">\n          <div className=\"preference-header\">\n            <label>Disable Dev Tools for this project</label>\n            <p className=\"preference-description\">\n              To disable this UI completely, set{' '}\n              <code className=\"dev-tools-info-code\">devIndicators: false</code>{' '}\n              in your <code className=\"dev-tools-info-code\">next.config</code>{' '}\n              file.\n            </p>\n          </div>\n        </div>\n      </div>\n    </DevToolsInfo>\n  )\n}\n\nfunction ThemeIcon({ theme }: { theme: 'dark' | 'light' | 'system' }) {\n  switch (theme) {\n    case 'system':\n      return <SystemIcon />\n    case 'dark':\n      return <DarkIcon />\n    case 'light':\n      return <LightIcon />\n    default:\n      return null\n  }\n}\n\nexport const DEV_TOOLS_INFO_USER_PREFERENCES_STYLES = css`\n  .preferences-container {\n    padding: 8px 6px;\n    width: 100%;\n  }\n\n  @media (min-width: 576px) {\n    .preferences-container {\n      width: 480px;\n    }\n  }\n\n  .preference-section:first-child {\n    padding-top: 0;\n  }\n\n  .preference-section {\n    padding: 12px 0;\n    border-bottom: 1px solid var(--color-gray-400);\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    gap: 24px;\n  }\n\n  .preference-section:last-child {\n    border-bottom: none;\n  }\n\n  .preference-header {\n    margin-bottom: 0;\n    flex: 1;\n  }\n\n  .preference-header label {\n    font-size: var(--size-14);\n    font-weight: 500;\n    color: var(--color-gray-1000);\n    margin: 0;\n  }\n\n  .preference-description {\n    color: var(--color-gray-900);\n    font-size: var(--size-14);\n    margin: 0;\n  }\n\n  .preference-icon {\n    display: flex;\n    align-items: center;\n    width: 16px;\n    height: 16px;\n  }\n\n  .select-button,\n  .action-button {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    background: var(--color-background-100);\n    border: 1px solid var(--color-gray-400);\n    border-radius: var(--rounded-lg);\n    font-weight: 400;\n    font-size: var(--size-14);\n    color: var(--color-gray-1000);\n    padding: 6px 8px;\n\n    &:hover {\n      background: var(--color-gray-100);\n    }\n  }\n\n  .preference-control-select {\n    padding: 6px 8px;\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    border-radius: var(--rounded-lg);\n    border: 1px solid var(--color-gray-400);\n\n    &:hover {\n      background: var(--color-gray-100);\n    }\n\n    &:focus-within {\n      outline: var(--focus-ring);\n    }\n  }\n\n  .preference-control-select select {\n    font-size: var(--size-14);\n    font-weight: 400;\n    border: none;\n    padding: 0 6px 0 0;\n    border-radius: 0;\n    outline: none;\n    background: none;\n  }\n\n  :global(.icon) {\n    width: 18px;\n    height: 18px;\n    color: #666;\n  }\n`\n"], "names": ["DEV_TOOLS_INFO_USER_PREFERENCES_STYLES", "UserPreferences", "getInitialPreference", "localStorage", "theme", "getItem", "STORAGE_KEY_THEME", "setPosition", "position", "hide", "props", "setTheme", "useState", "handleThemeChange", "e", "portal", "document", "querySelector", "target", "value", "classList", "remove", "removeItem", "add", "setItem", "handlePositionChange", "STORAGE_KEY_POSITION", "DevToolsInfo", "title", "div", "className", "label", "htmlFor", "p", "ThemeIcon", "select", "id", "name", "onChange", "option", "button", "data-hide-dev-tools", "onClick", "EyeIcon", "span", "code", "SystemIcon", "DarkIcon", "LightIcon", "css"], "mappings": ";;;;;;;;;;;;;;;IAwKaA,sCAAsC;eAAtCA;;IApJGC,eAAe;eAAfA;;;;;;uBApByB;qBACrB;kEACA;wBACoC;oEAClC;mEACD;qEACE;8BAEM;;;;;;;;;;AAG7B,SAASC;IACP,IAAI,OAAOC,iBAAiB,aAAa;QACvC,OAAO;IACT;IAEA,MAAMC,QAAQD,aAAaE,OAAO,CAACC,yBAAiB;IACpD,OAAOF,UAAU,UAAUA,UAAU,UAAUA,QAAQ;AACzD;AAEO,SAASH,gBAAgB,KAUL;IAVK,IAAA,EAC9BM,WAAW,EACXC,QAAQ,EACRC,IAAI,EACJ,GAAGC,OAMsB,GAVK;IAW9B,8CAA8C;IAC9C,MAAM,CAACN,OAAOO,SAAS,GAAGC,IAAAA,eAAQ,EAACV;IAEnC,MAAMW,oBAAoB,CAACC;QACzB,MAAMC,SAASC,SAASC,aAAa,CAAC;QACtC,IAAI,CAACF,QAAQ;QAEbJ,SAASG,EAAEI,MAAM,CAACC,KAAK;QAEvB,IAAIL,EAAEI,MAAM,CAACC,KAAK,KAAK,UAAU;YAC/BJ,OAAOK,SAAS,CAACC,MAAM,CAAC;YACxBN,OAAOK,SAAS,CAACC,MAAM,CAAC;YACxBlB,aAAamB,UAAU,CAAChB,yBAAiB;YACzC;QACF;QAEA,IAAIQ,EAAEI,MAAM,CAACC,KAAK,KAAK,QAAQ;YAC7BJ,OAAOK,SAAS,CAACG,GAAG,CAAC;YACrBR,OAAOK,SAAS,CAACC,MAAM,CAAC;YACxBlB,aAAaqB,OAAO,CAAClB,yBAAiB,EAAE;QAC1C,OAAO;YACLS,OAAOK,SAAS,CAACC,MAAM,CAAC;YACxBN,OAAOK,SAAS,CAACG,GAAG,CAAC;YACrBpB,aAAaqB,OAAO,CAAClB,yBAAiB,EAAE;QAC1C;IACF;IAEA,SAASmB,qBAAqBX,CAAuC;QACnEP,YAAYO,EAAEI,MAAM,CAACC,KAAK;QAC1BhB,aAAaqB,OAAO,CAACE,4BAAoB,EAAEZ,EAAEI,MAAM,CAACC,KAAK;IAC3D;IAEA,qBACE,qBAACQ,0BAAY;QAACC,OAAM;QAAe,GAAGlB,KAAK;kBACzC,cAAA,sBAACmB;YAAIC,WAAU;;8BACb,sBAACD;oBAAIC,WAAU;;sCACb,sBAACD;4BAAIC,WAAU;;8CACb,qBAACC;oCAAMC,SAAQ;8CAAQ;;8CACvB,qBAACC;oCAAEH,WAAU;8CAAyB;;;;sCAIxC,sBAACD;4BAAIC,WAAU;;8CACb,qBAACD;oCAAIC,WAAU;8CACb,cAAA,qBAACI;wCAAU9B,OAAOA;;;8CAEpB,sBAAC+B;oCACCC,IAAG;oCACHC,MAAK;oCACLP,WAAU;oCACVX,OAAOf;oCACPkC,UAAUzB;;sDAEV,qBAAC0B;4CAAOpB,OAAM;sDAAS;;sDACvB,qBAACoB;4CAAOpB,OAAM;sDAAQ;;sDACtB,qBAACoB;4CAAOpB,OAAM;sDAAO;;;;;;;;8BAK3B,sBAACU;oBAAIC,WAAU;;sCACb,sBAACD;4BAAIC,WAAU;;8CACb,qBAACC;oCAAMC,SAAQ;8CAAW;;8CAC1B,qBAACC;oCAAEH,WAAU;8CAAyB;;;;sCAIxC,qBAACD;4BAAIC,WAAU;sCACb,cAAA,sBAACK;gCACCC,IAAG;gCACHC,MAAK;gCACLP,WAAU;gCACVX,OAAOX;gCACP8B,UAAUb;;kDAEV,qBAACc;wCAAOpB,OAAM;kDAAc;;kDAC5B,qBAACoB;wCAAOpB,OAAM;kDAAe;;kDAC7B,qBAACoB;wCAAOpB,OAAM;kDAAW;;kDACzB,qBAACoB;wCAAOpB,OAAM;kDAAY;;;;;;;8BAKhC,sBAACU;oBAAIC,WAAU;;sCACb,sBAACD;4BAAIC,WAAU;;8CACb,qBAACC;oCAAMC,SAAQ;8CAAiB;;8CAGhC,qBAACC;oCAAEH,WAAU;8CAAyB;;;;sCAIxC,qBAACD;4BAAIC,WAAU;sCACb,cAAA,sBAACU;gCACCJ,IAAG;gCACHC,MAAK;gCACLI,qBAAmB;gCACnBX,WAAU;gCACVY,SAASjC;;kDAET,qBAACoB;wCAAIC,WAAU;kDACb,cAAA,qBAACa,gBAAO;;kDAEV,qBAACC;kDAAK;;;;;;;8BAKZ,qBAACf;oBAAIC,WAAU;8BACb,cAAA,sBAACD;wBAAIC,WAAU;;0CACb,qBAACC;0CAAM;;0CACP,sBAACE;gCAAEH,WAAU;;oCAAyB;oCACD;kDACnC,qBAACe;wCAAKf,WAAU;kDAAsB;;oCAA4B;oCAAI;kDAC9D,qBAACe;wCAAKf,WAAU;kDAAsB;;oCAAmB;oCAAI;;;;;;;;;AAQnF;AAEA,SAASI,UAAU,KAAiD;IAAjD,IAAA,EAAE9B,KAAK,EAA0C,GAAjD;IACjB,OAAQA;QACN,KAAK;YACH,qBAAO,qBAAC0C,mBAAU;QACpB,KAAK;YACH,qBAAO,qBAACC,iBAAQ;QAClB,KAAK;YACH,qBAAO,qBAACC,kBAAS;QACnB;YACE,OAAO;IACX;AACF;AAEO,MAAMhD,6CAAyCiD,QAAG"}