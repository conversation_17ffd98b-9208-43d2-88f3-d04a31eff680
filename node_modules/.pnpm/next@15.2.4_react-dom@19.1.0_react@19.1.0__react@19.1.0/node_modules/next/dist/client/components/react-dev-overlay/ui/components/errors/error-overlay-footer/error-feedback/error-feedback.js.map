{"version": 3, "sources": ["../../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-feedback/error-feedback.tsx"], "sourcesContent": ["import { useState, useCallback } from 'react'\nimport { ThumbsUp } from '../../../../icons/thumbs/thumbs-up'\nimport { ThumbsDown } from '../../../../icons/thumbs/thumbs-down'\nimport { cx } from '../../../../utils/cx'\n\ninterface ErrorFeedbackProps {\n  errorCode: string\n  className?: string\n}\nexport function ErrorFeedback({ errorCode, className }: ErrorFeedbackProps) {\n  const [votedMap, setVotedMap] = useState<Record<string, boolean>>({})\n  const voted = votedMap[errorCode]\n  const hasVoted = voted !== undefined\n  const disabled = process.env.__NEXT_TELEMETRY_DISABLED\n\n  const handleFeedback = useCallback(\n    async (wasHelpful: boolean) => {\n      // Optimistically set feedback state without loading/error states to keep implementation simple\n      setVotedMap((prev) => ({\n        ...prev,\n        [errorCode]: wasHelpful,\n      }))\n\n      try {\n        const response = await fetch(\n          `${process.env.__NEXT_ROUTER_BASEPATH || ''}/__nextjs_error_feedback?${new URLSearchParams(\n            {\n              errorCode,\n              wasHelpful: wasHelpful.toString(),\n            }\n          )}`\n        )\n\n        if (!response.ok) {\n          // Handle non-2xx HTTP responses here if needed\n          console.error('Failed to record feedback on the server.')\n        }\n      } catch (error) {\n        console.error('Failed to record feedback:', error)\n      }\n    },\n    [errorCode]\n  )\n\n  return (\n    <div\n      className={cx('error-feedback', className)}\n      role=\"region\"\n      aria-label=\"Error feedback\"\n    >\n      {hasVoted ? (\n        <p className=\"error-feedback-thanks\" role=\"status\" aria-live=\"polite\">\n          Thanks for your feedback!\n        </p>\n      ) : (\n        <>\n          <p>\n            <a\n              href=\"https://nextjs.org/telemetry#error-feedback\"\n              rel=\"noopener noreferrer\"\n              target=\"_blank\"\n            >\n              Was this helpful?\n            </a>\n          </p>\n          <button\n            aria-disabled={disabled ? 'true' : undefined}\n            aria-label=\"Mark as helpful\"\n            onClick={disabled ? undefined : () => handleFeedback(true)}\n            className={cx('feedback-button', voted === true && 'voted')}\n            title={\n              disabled\n                ? 'Feedback disabled due to setting NEXT_TELEMETRY_DISABLED'\n                : undefined\n            }\n            type=\"button\"\n          >\n            <ThumbsUp aria-hidden=\"true\" />\n          </button>\n          <button\n            aria-disabled={disabled ? 'true' : undefined}\n            aria-label=\"Mark as not helpful\"\n            onClick={disabled ? undefined : () => handleFeedback(false)}\n            className={cx('feedback-button', voted === false && 'voted')}\n            title={\n              disabled\n                ? 'Feedback disabled due to setting NEXT_TELEMETRY_DISABLED'\n                : undefined\n            }\n            type=\"button\"\n          >\n            <ThumbsDown\n              aria-hidden=\"true\"\n              // Optical alignment\n              style={{\n                translate: '1px 1px',\n              }}\n            />\n          </button>\n        </>\n      )}\n    </div>\n  )\n}\n\nexport const styles = `\n  .error-feedback {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    white-space: nowrap;\n    color: var(--color-gray-900);\n  }\n\n  .error-feedback-thanks {\n    height: var(--size-24);\n    display: flex;\n    align-items: center;\n    padding-right: 4px; /* To match the 4px inner padding of the thumbs up and down icons */\n  }\n\n  .feedback-button {\n    background: none;\n    border: none;\n    border-radius: var(--rounded-md);\n    width: var(--size-24);\n    height: var(--size-24);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    cursor: pointer;\n\n    &:focus {\n      outline: var(--focus-ring);\n    }\n\n    &:hover {\n      background: var(--color-gray-alpha-100);\n    }\n\n    &:active {\n      background: var(--color-gray-alpha-200);\n    }\n  }\n\n  .feedback-button[aria-disabled='true'] {\n    opacity: 0.7;\n    cursor: not-allowed;\n  }\n\n  .feedback-button.voted {\n    background: var(--color-gray-alpha-200);\n  }\n\n  .thumbs-up-icon,\n  .thumbs-down-icon {\n    color: var(--color-gray-900);\n    width: var(--size-16);\n    height: var(--size-16);\n  }\n`\n"], "names": ["ErrorFeedback", "styles", "errorCode", "className", "votedMap", "setVotedMap", "useState", "voted", "hasVoted", "undefined", "disabled", "process", "env", "__NEXT_TELEMETRY_DISABLED", "handleFeedback", "useCallback", "wasHelpful", "prev", "response", "fetch", "__NEXT_ROUTER_BASEPATH", "URLSearchParams", "toString", "ok", "console", "error", "div", "cx", "role", "aria-label", "p", "aria-live", "a", "href", "rel", "target", "button", "aria-disabled", "onClick", "title", "type", "ThumbsUp", "aria-hidden", "ThumbsDown", "style", "translate"], "mappings": ";;;;;;;;;;;;;;;IASgBA,aAAa;eAAbA;;IAgGHC,MAAM;eAANA;;;;uBAzGyB;0BACb;4BACE;oBACR;AAMZ,SAASD,cAAc,KAA4C;IAA5C,IAAA,EAAEE,SAAS,EAAEC,SAAS,EAAsB,GAA5C;IAC5B,MAAM,CAACC,UAAUC,YAAY,GAAGC,IAAAA,eAAQ,EAA0B,CAAC;IACnE,MAAMC,QAAQH,QAAQ,CAACF,UAAU;IACjC,MAAMM,WAAWD,UAAUE;IAC3B,MAAMC,WAAWC,QAAQC,GAAG,CAACC,yBAAyB;IAEtD,MAAMC,iBAAiBC,IAAAA,kBAAW,EAChC,OAAOC;QACL,+FAA+F;QAC/FX,YAAY,CAACY,OAAU,CAAA;gBACrB,GAAGA,IAAI;gBACP,CAACf,UAAU,EAAEc;YACf,CAAA;QAEA,IAAI;YACF,MAAME,WAAW,MAAMC,MACrB,AAAGR,CAAAA,QAAQC,GAAG,CAACQ,sBAAsB,IAAI,EAAC,IAAE,8BAA2B,IAAIC,gBACzE;gBACEnB;gBACAc,YAAYA,WAAWM,QAAQ;YACjC;YAIJ,IAAI,CAACJ,SAASK,EAAE,EAAE;gBAChB,+CAA+C;gBAC/CC,QAAQC,KAAK,CAAC;YAChB;QACF,EAAE,OAAOA,OAAO;YACdD,QAAQC,KAAK,CAAC,8BAA8BA;QAC9C;IACF,GACA;QAACvB;KAAU;IAGb,qBACE,qBAACwB;QACCvB,WAAWwB,IAAAA,MAAE,EAAC,kBAAkBxB;QAChCyB,MAAK;QACLC,cAAW;kBAEVrB,yBACC,qBAACsB;YAAE3B,WAAU;YAAwByB,MAAK;YAASG,aAAU;sBAAS;2BAItE;;8BACE,qBAACD;8BACC,cAAA,qBAACE;wBACCC,MAAK;wBACLC,KAAI;wBACJC,QAAO;kCACR;;;8BAIH,qBAACC;oBACCC,iBAAe3B,WAAW,SAASD;oBACnCoB,cAAW;oBACXS,SAAS5B,WAAWD,YAAY,IAAMK,eAAe;oBACrDX,WAAWwB,IAAAA,MAAE,EAAC,mBAAmBpB,UAAU,QAAQ;oBACnDgC,OACE7B,WACI,6DACAD;oBAEN+B,MAAK;8BAEL,cAAA,qBAACC,kBAAQ;wBAACC,eAAY;;;8BAExB,qBAACN;oBACCC,iBAAe3B,WAAW,SAASD;oBACnCoB,cAAW;oBACXS,SAAS5B,WAAWD,YAAY,IAAMK,eAAe;oBACrDX,WAAWwB,IAAAA,MAAE,EAAC,mBAAmBpB,UAAU,SAAS;oBACpDgC,OACE7B,WACI,6DACAD;oBAEN+B,MAAK;8BAEL,cAAA,qBAACG,sBAAU;wBACTD,eAAY;wBACZ,oBAAoB;wBACpBE,OAAO;4BACLC,WAAW;wBACb;;;;;;AAOd;AAEO,MAAM5C,SAAU"}