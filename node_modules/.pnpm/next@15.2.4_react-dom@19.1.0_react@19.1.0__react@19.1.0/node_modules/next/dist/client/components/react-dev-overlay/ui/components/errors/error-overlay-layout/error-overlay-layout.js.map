{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/error-overlay-layout/error-overlay-layout.tsx"], "sourcesContent": ["import * as React from 'react'\nimport type { DebugInfo } from '../../../../types'\nimport type { ErrorMessageType } from '../error-message/error-message'\nimport type { ErrorType } from '../error-type-label/error-type-label'\n\nimport { <PERSON><PERSON><PERSON>onte<PERSON>, DialogFooter } from '../../dialog'\nimport {\n  ErrorOverlayToolbar,\n  styles as toolbarStyles,\n} from '../error-overlay-toolbar/error-overlay-toolbar'\nimport { ErrorOverlayFooter } from '../error-overlay-footer/error-overlay-footer'\nimport {\n  ErrorMessage,\n  styles as errorMessageStyles,\n} from '../error-message/error-message'\nimport {\n  ErrorTypeLabel,\n  styles as errorTypeLabelStyles,\n} from '../error-type-label/error-type-label'\nimport {\n  ErrorOverlayNav,\n  styles as floatingHeaderStyles,\n} from '../error-overlay-nav/error-overlay-nav'\n\nimport { ErrorOverlayDialog, DIALOG_STYLES } from '../dialog/dialog'\nimport {\n  ErrorOverlayDialogHeader,\n  DIALOG_HEADER_STYLES,\n} from '../dialog/header'\nimport { ErrorOverlayDialogBody, DIALOG_BODY_STYLES } from '../dialog/body'\nimport { CALL_STACK_STYLES } from '../call-stack/call-stack'\nimport { OVERLAY_STYLES, ErrorOverlayOverlay } from '../overlay/overlay'\nimport { ErrorOverlayBottomStack } from '../error-overlay-bottom-stack'\nimport type { ErrorBaseProps } from '../error-overlay/error-overlay'\nimport type { ReadyRuntimeError } from '../../../../utils/get-error-by-type'\nimport { EnvironmentNameLabel } from '../environment-name-label/environment-name-label'\nimport { useFocusTrap } from '../dev-tools-indicator/utils'\n\ninterface ErrorOverlayLayoutProps extends ErrorBaseProps {\n  errorMessage: ErrorMessageType\n  errorType: ErrorType\n  children?: React.ReactNode\n  errorCode?: string\n  error: ReadyRuntimeError['error']\n  debugInfo?: DebugInfo\n  isBuildError?: boolean\n  onClose?: () => void\n  // TODO: better handle receiving\n  runtimeErrors?: ReadyRuntimeError[]\n  activeIdx?: number\n  setActiveIndex?: (index: number) => void\n  footerMessage?: string\n  dialogResizerRef?: React.RefObject<HTMLDivElement | null>\n}\n\nexport function ErrorOverlayLayout({\n  errorMessage,\n  errorType,\n  children,\n  errorCode,\n  error,\n  debugInfo,\n  isBuildError,\n  onClose,\n  versionInfo,\n  runtimeErrors,\n  activeIdx,\n  setActiveIndex,\n  footerMessage,\n  isTurbopack,\n  dialogResizerRef,\n  // This prop is used to animate the dialog, it comes from a parent component (<ErrorOverlay>)\n  // If it's not being passed, we should just render the component as it is being\n  // used without the context of a parent component that controls its state (e.g. Storybook).\n  rendered = true,\n  transitionDurationMs,\n}: ErrorOverlayLayoutProps) {\n  const animationProps = {\n    'data-rendered': rendered,\n    style: {\n      '--transition-duration': `${transitionDurationMs}ms`,\n    } as React.CSSProperties,\n  }\n\n  const hasFooter = Boolean(footerMessage || errorCode)\n  const dialogRef = React.useRef<HTMLDivElement | null>(null)\n  useFocusTrap(dialogRef, null, rendered)\n\n  return (\n    <ErrorOverlayOverlay fixed={isBuildError} {...animationProps}>\n      <div data-nextjs-dialog-root ref={dialogRef} {...animationProps}>\n        <ErrorOverlayDialog\n          onClose={onClose}\n          dialogResizerRef={dialogResizerRef}\n          data-has-footer={hasFooter}\n        >\n          <DialogContent>\n            <ErrorOverlayDialogHeader>\n              <div\n                className=\"nextjs__container_errors__error_title\"\n                // allow assertion in tests before error rating is implemented\n                data-nextjs-error-code={errorCode}\n              >\n                <span data-nextjs-error-label-group>\n                  <ErrorTypeLabel errorType={errorType} />\n                  {error.environmentName && (\n                    <EnvironmentNameLabel\n                      environmentName={error.environmentName}\n                    />\n                  )}\n                </span>\n                <ErrorOverlayToolbar error={error} debugInfo={debugInfo} />\n              </div>\n              <ErrorMessage errorMessage={errorMessage} />\n            </ErrorOverlayDialogHeader>\n\n            <ErrorOverlayDialogBody>{children}</ErrorOverlayDialogBody>\n          </DialogContent>\n          {hasFooter && (\n            <DialogFooter>\n              <ErrorOverlayFooter\n                footerMessage={footerMessage}\n                errorCode={errorCode}\n              />\n            </DialogFooter>\n          )}\n          <ErrorOverlayBottomStack\n            errorCount={runtimeErrors?.length ?? 0}\n            activeIdx={activeIdx ?? 0}\n          />\n        </ErrorOverlayDialog>\n        <ErrorOverlayNav\n          runtimeErrors={runtimeErrors}\n          activeIdx={activeIdx}\n          setActiveIndex={setActiveIndex}\n          versionInfo={versionInfo}\n          isTurbopack={isTurbopack}\n        />\n      </div>\n    </ErrorOverlayOverlay>\n  )\n}\n\nexport const styles = `\n  ${OVERLAY_STYLES}\n  ${DIALOG_STYLES}\n  ${DIALOG_HEADER_STYLES}\n  ${DIALOG_BODY_STYLES}\n\n  ${floatingHeaderStyles}\n  ${errorTypeLabelStyles}\n  ${errorMessageStyles}\n  ${toolbarStyles}\n  ${CALL_STACK_STYLES}\n\n  [data-nextjs-error-label-group] {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n  }\n`\n"], "names": ["ErrorOverlayLayout", "styles", "errorMessage", "errorType", "children", "errorCode", "error", "debugInfo", "isBuildError", "onClose", "versionInfo", "runtimeErrors", "activeIdx", "setActiveIndex", "footerMessage", "isTurbopack", "dialogResizerRef", "rendered", "transitionDurationMs", "animationProps", "style", "<PERSON><PERSON><PERSON>er", "Boolean", "dialogRef", "React", "useRef", "useFocusTrap", "ErrorOverlayOverlay", "fixed", "div", "data-nextjs-dialog-root", "ref", "ErrorOverlayDialog", "data-has-footer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ErrorOverlayDialogHeader", "className", "data-nextjs-error-code", "span", "data-nextjs-error-label-group", "ErrorTypeLabel", "environmentName", "EnvironmentNameLabel", "ErrorOverlayToolbar", "ErrorMessage", "ErrorOverlayDialogBody", "<PERSON><PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ErrorOverlayBottomStack", "errorCount", "length", "ErrorOverlayNav", "OVERLAY_STYLES", "DIALOG_STYLES", "DIALOG_HEADER_STYLES", "DIALOG_BODY_STYLES", "floatingHeaderStyles", "errorTypeLabelStyles", "errorMessageStyles", "toolbarStyles", "CALL_STACK_STYLES"], "mappings": ";;;;;;;;;;;;;;;IAuDgBA,kBAAkB;eAAlBA;;IAwFHC,MAAM;eAANA;;;;;iEA/IU;wBAKqB;qCAIrC;oCAC4B;8BAI5B;gCAIA;iCAIA;yBAE2C;wBAI3C;sBACoD;2BACzB;yBACkB;yCACZ;sCAGH;uBACR;AAmBtB,SAASD,mBAAmB,KAqBT;IArBS,IAAA,EACjCE,YAAY,EACZC,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,SAAS,EACTC,YAAY,EACZC,OAAO,EACPC,WAAW,EACXC,aAAa,EACbC,SAAS,EACTC,cAAc,EACdC,aAAa,EACbC,WAAW,EACXC,gBAAgB,EAChB,6FAA6F;IAC7F,+EAA+E;IAC/E,2FAA2F;IAC3FC,WAAW,IAAI,EACfC,oBAAoB,EACI,GArBS;IAsBjC,MAAMC,iBAAiB;QACrB,iBAAiBF;QACjBG,OAAO;YACL,yBAAyB,AAAC,KAAEF,uBAAqB;QACnD;IACF;IAEA,MAAMG,YAAYC,QAAQR,iBAAiBT;IAC3C,MAAMkB,YAAYC,OAAMC,MAAM,CAAwB;IACtDC,IAAAA,mBAAY,EAACH,WAAW,MAAMN;QAyCRN;IAvCtB,qBACE,qBAACgB,4BAAmB;QAACC,OAAOpB;QAAe,GAAGW,cAAc;kBAC1D,cAAA,sBAACU;YAAIC,yBAAuB;YAACC,KAAKR;YAAY,GAAGJ,cAAc;;8BAC7D,sBAACa,2BAAkB;oBACjBvB,SAASA;oBACTO,kBAAkBA;oBAClBiB,mBAAiBZ;;sCAEjB,sBAACa,qBAAa;;8CACZ,sBAACC,gCAAwB;;sDACvB,sBAACN;4CACCO,WAAU;4CACV,8DAA8D;4CAC9DC,0BAAwBhC;;8DAExB,sBAACiC;oDAAKC,+BAA6B;;sEACjC,qBAACC,8BAAc;4DAACrC,WAAWA;;wDAC1BG,MAAMmC,eAAe,kBACpB,qBAACC,0CAAoB;4DACnBD,iBAAiBnC,MAAMmC,eAAe;;;;8DAI5C,qBAACE,wCAAmB;oDAACrC,OAAOA;oDAAOC,WAAWA;;;;sDAEhD,qBAACqC,0BAAY;4CAAC1C,cAAcA;;;;8CAG9B,qBAAC2C,4BAAsB;8CAAEzC;;;;wBAE1BiB,2BACC,qBAACyB,oBAAY;sCACX,cAAA,qBAACC,sCAAkB;gCACjBjC,eAAeA;gCACfT,WAAWA;;;sCAIjB,qBAAC2C,gDAAuB;4BACtBC,YAAYtC,CAAAA,wBAAAA,iCAAAA,cAAeuC,MAAM,YAArBvC,wBAAyB;4BACrCC,WAAWA,oBAAAA,YAAa;;;;8BAG5B,qBAACuC,gCAAe;oBACdxC,eAAeA;oBACfC,WAAWA;oBACXC,gBAAgBA;oBAChBH,aAAaA;oBACbK,aAAaA;;;;;AAKvB;AAEO,MAAMd,SAAS,AAAC,SACnBmD,uBAAc,GAAC,SACfC,sBAAa,GAAC,SACdC,4BAAoB,GAAC,SACrBC,wBAAkB,GAAC,WAEnBC,uBAAoB,GAAC,SACrBC,sBAAoB,GAAC,SACrBC,oBAAkB,GAAC,SACnBC,2BAAa,GAAC,SACdC,4BAAiB,GAAC"}