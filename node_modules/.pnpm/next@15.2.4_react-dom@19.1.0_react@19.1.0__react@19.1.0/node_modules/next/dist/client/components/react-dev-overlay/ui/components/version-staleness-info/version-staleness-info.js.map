{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/version-staleness-info/version-staleness-info.tsx"], "sourcesContent": ["import type { VersionInfo } from '../../../../../../server/dev/parse-version-info'\nimport { cx } from '../../utils/cx'\n\nexport function VersionStalenessInfo({\n  versionInfo,\n  isTurbopack,\n}: {\n  versionInfo: VersionInfo\n  isTurbopack?: boolean\n}) {\n  const { staleness } = versionInfo\n  let { text, indicatorClass, title } = getStaleness(versionInfo)\n\n  const shouldBeLink = staleness.startsWith('stale')\n  if (shouldBeLink) {\n    return (\n      <a\n        className={cx(\n          'nextjs-container-build-error-version-status',\n          'dialog-exclude-closing-from-outside-click',\n          isTurbopack && 'turbopack-border'\n        )}\n        target=\"_blank\"\n        rel=\"noopener noreferrer\"\n        href=\"https://nextjs.org/docs/messages/version-staleness\"\n      >\n        <Eclipse\n          className={cx('version-staleness-indicator', indicatorClass)}\n        />\n        <span data-nextjs-version-checker title={title}>\n          {text}\n        </span>\n        {isTurbopack && <span className=\"turbopack-text\">Turbopack</span>}\n      </a>\n    )\n  }\n\n  return (\n    <span className=\"nextjs-container-build-error-version-status dialog-exclude-closing-from-outside-click\">\n      <Eclipse className={cx('version-staleness-indicator', indicatorClass)} />\n      <span data-nextjs-version-checker title={title}>\n        {text}\n      </span>\n      {isTurbopack && <span className=\"turbopack-text\">Turbopack</span>}\n    </span>\n  )\n}\n\nexport function getStaleness({ installed, staleness, expected }: VersionInfo) {\n  let text = ''\n  let title = ''\n  let indicatorClass = ''\n  const versionLabel = `Next.js ${installed}`\n  switch (staleness) {\n    case 'newer-than-npm':\n    case 'fresh':\n      text = versionLabel\n      title = `Latest available version is detected (${installed}).`\n      indicatorClass = 'fresh'\n      break\n    case 'stale-patch':\n    case 'stale-minor':\n      text = `${versionLabel} (stale)`\n      title = `There is a newer version (${expected}) available, upgrade recommended! `\n      indicatorClass = 'stale'\n      break\n    case 'stale-major': {\n      text = `${versionLabel} (outdated)`\n      title = `An outdated version detected (latest is ${expected}), upgrade is highly recommended!`\n      indicatorClass = 'outdated'\n      break\n    }\n    case 'stale-prerelease': {\n      text = `${versionLabel} (stale)`\n      title = `There is a newer canary version (${expected}) available, please upgrade! `\n      indicatorClass = 'stale'\n      break\n    }\n    case 'unknown':\n      text = `${versionLabel} (unknown)`\n      title = 'No Next.js version data was found.'\n      indicatorClass = 'unknown'\n      break\n    default:\n      break\n  }\n  return { text, indicatorClass, title }\n}\n\nexport const styles = `\n  .nextjs-container-build-error-version-status {\n    -webkit-font-smoothing: antialiased;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    gap: 4px;\n\n    height: var(--size-26);\n    padding: 6px 8px 6px 6px;\n    background: var(--color-background-100);\n    background-clip: padding-box;\n    border: 1px solid var(--color-gray-alpha-400);\n    box-shadow: var(--shadow-small);\n    border-radius: var(--rounded-full);\n\n    color: var(--color-gray-900);\n    font-size: var(--size-12);\n    font-weight: 500;\n    line-height: var(--size-16);\n  }\n\n  a.nextjs-container-build-error-version-status {\n    text-decoration: none;\n    color: var(--color-gray-900);\n\n    &:hover {\n      background: var(--color-gray-100);\n    }\n\n    &:focus {\n      outline: var(--focus-ring);\n    }\n  }\n\n  .version-staleness-indicator.fresh {\n    fill: var(--color-green-800);\n    stroke: var(--color-green-300);\n  }\n  .version-staleness-indicator.stale {\n    fill: var(--color-amber-800);\n    stroke: var(--color-amber-300);\n  }\n  .version-staleness-indicator.outdated {\n    fill: var(--color-red-800);\n    stroke: var(--color-red-300);\n  }\n  .version-staleness-indicator.unknown {\n    fill: var(--color-gray-800);\n    stroke: var(--color-gray-300);\n  }\n\n  .nextjs-container-build-error-version-status > .turbopack-text {\n    background: linear-gradient(\n      to right,\n      var(--color-turbopack-text-red) 0%,\n      var(--color-turbopack-text-blue) 100%\n    );\n    background-clip: text;\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n  }\n`\n\nfunction Eclipse({ className }: { className: string }) {\n  return (\n    <svg\n      width=\"14\"\n      height=\"14\"\n      viewBox=\"0 0 14 14\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <circle className={className} cx=\"7\" cy=\"7\" r=\"5.5\" strokeWidth=\"3\" />\n    </svg>\n  )\n}\n"], "names": ["VersionStalenessInfo", "getStaleness", "styles", "versionInfo", "isTurbopack", "staleness", "text", "indicatorClass", "title", "shouldBeLink", "startsWith", "a", "className", "cx", "target", "rel", "href", "Eclipse", "span", "data-nextjs-version-checker", "installed", "expected", "versionLabel", "svg", "width", "height", "viewBox", "fill", "xmlns", "circle", "cy", "r", "strokeWidth"], "mappings": ";;;;;;;;;;;;;;;;IAGgBA,oBAAoB;eAApBA;;IA6CAC,YAAY;eAAZA;;IAyCHC,MAAM;eAANA;;;;oBAxFM;AAEZ,SAASF,qBAAqB,KAMpC;IANoC,IAAA,EACnCG,WAAW,EACXC,WAAW,EAIZ,GANoC;IAOnC,MAAM,EAAEC,SAAS,EAAE,GAAGF;IACtB,IAAI,EAAEG,IAAI,EAAEC,cAAc,EAAEC,KAAK,EAAE,GAAGP,aAAaE;IAEnD,MAAMM,eAAeJ,UAAUK,UAAU,CAAC;IAC1C,IAAID,cAAc;QAChB,qBACE,sBAACE;YACCC,WAAWC,IAAAA,MAAE,EACX,+CACA,6CACAT,eAAe;YAEjBU,QAAO;YACPC,KAAI;YACJC,MAAK;;8BAEL,qBAACC;oBACCL,WAAWC,IAAAA,MAAE,EAAC,+BAA+BN;;8BAE/C,qBAACW;oBAAKC,6BAA2B;oBAACX,OAAOA;8BACtCF;;gBAEFF,6BAAe,qBAACc;oBAAKN,WAAU;8BAAiB;;;;IAGvD;IAEA,qBACE,sBAACM;QAAKN,WAAU;;0BACd,qBAACK;gBAAQL,WAAWC,IAAAA,MAAE,EAAC,+BAA+BN;;0BACtD,qBAACW;gBAAKC,6BAA2B;gBAACX,OAAOA;0BACtCF;;YAEFF,6BAAe,qBAACc;gBAAKN,WAAU;0BAAiB;;;;AAGvD;AAEO,SAASX,aAAa,KAA+C;IAA/C,IAAA,EAAEmB,SAAS,EAAEf,SAAS,EAAEgB,QAAQ,EAAe,GAA/C;IAC3B,IAAIf,OAAO;IACX,IAAIE,QAAQ;IACZ,IAAID,iBAAiB;IACrB,MAAMe,eAAe,AAAC,aAAUF;IAChC,OAAQf;QACN,KAAK;QACL,KAAK;YACHC,OAAOgB;YACPd,QAAQ,AAAC,2CAAwCY,YAAU;YAC3Db,iBAAiB;YACjB;QACF,KAAK;QACL,KAAK;YACHD,OAAO,AAAC,KAAEgB,eAAa;YACvBd,QAAQ,AAAC,+BAA4Ba,WAAS;YAC9Cd,iBAAiB;YACjB;QACF,KAAK;YAAe;gBAClBD,OAAO,AAAC,KAAEgB,eAAa;gBACvBd,QAAQ,AAAC,6CAA0Ca,WAAS;gBAC5Dd,iBAAiB;gBACjB;YACF;QACA,KAAK;YAAoB;gBACvBD,OAAO,AAAC,KAAEgB,eAAa;gBACvBd,QAAQ,AAAC,sCAAmCa,WAAS;gBACrDd,iBAAiB;gBACjB;YACF;QACA,KAAK;YACHD,OAAO,AAAC,KAAEgB,eAAa;YACvBd,QAAQ;YACRD,iBAAiB;YACjB;QACF;YACE;IACJ;IACA,OAAO;QAAED;QAAMC;QAAgBC;IAAM;AACvC;AAEO,MAAMN,SAAU;AAgEvB,SAASe,QAAQ,KAAoC;IAApC,IAAA,EAAEL,SAAS,EAAyB,GAApC;IACf,qBACE,qBAACW;QACCC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;QACLC,OAAM;kBAEN,cAAA,qBAACC;YAAOjB,WAAWA;YAAWC,IAAG;YAAIiB,IAAG;YAAIC,GAAE;YAAMC,aAAY;;;AAGtE"}