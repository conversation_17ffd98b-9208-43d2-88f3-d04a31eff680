{"version": 3, "sources": ["../../../../src/client/components/router-reducer/invalidate-cache-below-flight-segmentpath.ts"], "sourcesContent": ["import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { FlightSegmentPath } from '../../../server/app-render/types'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport { getNextFlightSegmentPath } from '../../flight-data-helpers'\n\n/**\n * Fill cache up to the end of the flightSegmentPath, invalidating anything below it.\n */\nexport function invalidateCacheBelowFlightSegmentPath(\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightSegmentPath: FlightSegmentPath\n): void {\n  const isLastEntry = flightSegmentPath.length <= 2\n  const [parallelRouteKey, segment] = flightSegmentPath\n\n  const cacheKey = createRouterCacheKey(segment)\n\n  const existingChildSegmentMap =\n    existingCache.parallelRoutes.get(parallelRouteKey)\n\n  if (!existingChildSegmentMap) {\n    // Bailout because the existing cache does not have the path to the leaf node\n    // Will trigger lazy fetch in layout-router because of missing segment\n    return\n  }\n\n  let childSegmentMap = newCache.parallelRoutes.get(parallelRouteKey)\n  if (!childSegmentMap || childSegmentMap === existingChildSegmentMap) {\n    childSegmentMap = new Map(existingChildSegmentMap)\n    newCache.parallelRoutes.set(parallelRouteKey, childSegmentMap)\n  }\n\n  // In case of last entry don't copy further down.\n  if (isLastEntry) {\n    childSegmentMap.delete(cacheKey)\n    return\n  }\n\n  const existingChildCacheNode = existingChildSegmentMap.get(cacheKey)\n  let childCacheNode = childSegmentMap.get(cacheKey)\n\n  if (!childCacheNode || !existingChildCacheNode) {\n    // Bailout because the existing cache does not have the path to the leaf node\n    // Will trigger lazy fetch in layout-router because of missing segment\n    return\n  }\n\n  if (childCacheNode === existingChildCacheNode) {\n    childCacheNode = {\n      lazyData: childCacheNode.lazyData,\n      rsc: childCacheNode.rsc,\n      prefetchRsc: childCacheNode.prefetchRsc,\n      head: childCacheNode.head,\n      prefetchHead: childCacheNode.prefetchHead,\n      parallelRoutes: new Map(childCacheNode.parallelRoutes),\n    } as CacheNode\n    childSegmentMap.set(cacheKey, childCacheNode)\n  }\n\n  invalidateCacheBelowFlightSegmentPath(\n    childCacheNode,\n    existingChildCacheNode,\n    getNextFlightSegmentPath(flightSegmentPath)\n  )\n}\n"], "names": ["invalidateCacheBelowFlightSegmentPath", "newCache", "existingCache", "flightSegmentPath", "isLastEntry", "length", "parallelRouteKey", "segment", "cache<PERSON>ey", "createRouterCache<PERSON>ey", "existingChildSegmentMap", "parallelRoutes", "get", "childSegmentMap", "Map", "set", "delete", "existingChildCacheNode", "childCacheNode", "lazyData", "rsc", "prefetchRsc", "head", "prefetchHead", "getNextFlightSegmentPath"], "mappings": ";;;;+BAQgBA;;;eAAAA;;;sCANqB;mCACI;AAKlC,SAASA,sCACdC,QAAmB,EACnBC,aAAwB,EACxBC,iBAAoC;IAEpC,MAAMC,cAAcD,kBAAkBE,MAAM,IAAI;IAChD,MAAM,CAACC,kBAAkBC,QAAQ,GAAGJ;IAEpC,MAAMK,WAAWC,IAAAA,0CAAoB,EAACF;IAEtC,MAAMG,0BACJR,cAAcS,cAAc,CAACC,GAAG,CAACN;IAEnC,IAAI,CAACI,yBAAyB;QAC5B,6EAA6E;QAC7E,sEAAsE;QACtE;IACF;IAEA,IAAIG,kBAAkBZ,SAASU,cAAc,CAACC,GAAG,CAACN;IAClD,IAAI,CAACO,mBAAmBA,oBAAoBH,yBAAyB;QACnEG,kBAAkB,IAAIC,IAAIJ;QAC1BT,SAASU,cAAc,CAACI,GAAG,CAACT,kBAAkBO;IAChD;IAEA,iDAAiD;IACjD,IAAIT,aAAa;QACfS,gBAAgBG,MAAM,CAACR;QACvB;IACF;IAEA,MAAMS,yBAAyBP,wBAAwBE,GAAG,CAACJ;IAC3D,IAAIU,iBAAiBL,gBAAgBD,GAAG,CAACJ;IAEzC,IAAI,CAACU,kBAAkB,CAACD,wBAAwB;QAC9C,6EAA6E;QAC7E,sEAAsE;QACtE;IACF;IAEA,IAAIC,mBAAmBD,wBAAwB;QAC7CC,iBAAiB;YACfC,UAAUD,eAAeC,QAAQ;YACjCC,KAAKF,eAAeE,GAAG;YACvBC,aAAaH,eAAeG,WAAW;YACvCC,MAAMJ,eAAeI,IAAI;YACzBC,cAAcL,eAAeK,YAAY;YACzCZ,gBAAgB,IAAIG,IAAII,eAAeP,cAAc;QACvD;QACAE,gBAAgBE,GAAG,CAACP,UAAUU;IAChC;IAEAlB,sCACEkB,gBACAD,wBACAO,IAAAA,2CAAwB,EAACrB;AAE7B"}