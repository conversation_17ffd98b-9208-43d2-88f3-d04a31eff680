#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/sucrase@3.35.0/node_modules/sucrase/bin/node_modules:/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/sucrase@3.35.0/node_modules/sucrase/node_modules:/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/sucrase@3.35.0/node_modules:/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/sucrase@3.35.0/node_modules/sucrase/bin/node_modules:/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/sucrase@3.35.0/node_modules/sucrase/node_modules:/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/sucrase@3.35.0/node_modules:/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/sucrase" "$@"
else
  exec node  "$basedir/../../bin/sucrase" "$@"
fi
