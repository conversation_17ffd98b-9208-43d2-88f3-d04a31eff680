#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/update-browserslist-db@1.1.3_browserslist@4.25.0/node_modules/update-browserslist-db/node_modules:/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/update-browserslist-db@1.1.3_browserslist@4.25.0/node_modules:/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/update-browserslist-db@1.1.3_browserslist@4.25.0/node_modules/update-browserslist-db/node_modules:/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/update-browserslist-db@1.1.3_browserslist@4.25.0/node_modules:/Users/<USER>/Desktop/OR_EL RENTAL/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../cli.js" "$@"
else
  exec node  "$basedir/../../cli.js" "$@"
fi
